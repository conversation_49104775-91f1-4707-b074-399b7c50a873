import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:math' as math;
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';

class CustomBottomNav_PUPPII extends StatelessWidget {
  final int currentIndex_PUPPII;
  final Function(int) onTap_PUPPII;

  const CustomBottomNav_PUPPII({
    super.key,
    required this.currentIndex_PUPPII,
    required this.onTap_PUPPII,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 130.h,
      child: Stack(
        clipBehavior: Clip.none, // 允许阴影显示
        children: [
          // Fan-shaped background
          Positioned(
            bottom: 0,
            right: 0,
            child: CustomPaint(
              size: Size(130.w, 130.h),
              painter: FanShapePainter_PUPPII(),
            ),
          ),

          // Navigation icons arranged along fan arc
          ..._buildNavButtons(),
        ],
      ),
    );
  }

  List<Widget> _buildNavButtons() {
    final List<Widget> buttons = [];
    final List<String> iconPaths = [
      'assets_puppii/icons_puppii/首页2.png',
      'assets_puppii/icons_puppii/记录2.png',
      'assets_puppii/icons_puppii/我的2.png'
    ];

    // 扇形参数
    final double fanRadius = 90.w; // 按钮距离扇形中心的半径，大幅增加分散度
    final double fanSize = 150.w; // 扇形尺寸，增大以容纳更分散的按钮

    // 扇形中心点（右下角）
    final double centerX = fanSize;
    final double centerY = fanSize;

    // 三个按钮的角度：大幅分散的弧线分布
    // 扇形从右下角向左上角展开（180°-270°），按钮最大化分散
    final List<double> angles = [180, 225, 270]; // 40度大间距分布

    for (int i = 0; i < 3; i++) {
      final double angleRad = angles[i] * math.pi / 180; // 转换为弧度

      // 计算按钮在扇形弧线上的位置
      final double buttonX = centerX + fanRadius * math.cos(angleRad);
      final double buttonY = centerY + fanRadius * math.sin(angleRad);

      buttons.add(
        Positioned(
          right: fanSize - buttonX + 15.w, // 从扇形中心计算相对位置
          bottom: fanSize - buttonY + 15.h, // 从扇形中心计算相对位置
          child: _buildNavItem(
            iconPath: iconPaths[i],
            index: i,
            label: ['Home', 'History', 'Profile'][i],
          ),
        ),
      );
    }

    return buttons;
  }

  Widget _buildNavItem({
    required String iconPath,
    required int index,
    required String label,
  }) {
    final bool isSelected = currentIndex_PUPPII == index;
    // 根据激活状态选择图标路径
    final displayIconPath = isSelected
        ? iconPath.replaceAll('2.png', '.png') // 激活状态去掉2
        : iconPath; // 非激活状态保持原样

    return GestureDetector(
      onTap: () => onTap_PUPPII(index),
      child: Center(
        child: Image.asset(
          displayIconPath,
          width: 50.w,
          height: 50.w,
        ),
      ),
    );
  }
}

class FanShapePainter_PUPPII extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // 扇形中心点应该在右下角（画布的右下角）
    final center = Offset(size.width, size.height);
    final radius = size.width;

    // 创建阴影效果
    final shadowPaint = Paint()
      ..color = AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.6)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);

    // 绘制阴影扇形 - 从右下角向左上角展开（180度到270度）
    canvas.drawArc(
      Rect.fromCircle(center: center.translate(6, 6), radius: radius),
      3.14159, // 180度 - 水平向左开始
      1.5708, // 90度扫描角度 - 向上扫描到270度
      true,
      shadowPaint,
    );

    // 先绘制基础背景色
    final basePaint = Paint()
      ..color = AppTheme_PUPPII.neumorphismBase_PUPPII
      ..style = PaintingStyle.fill;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -1.5708,
      1.5708,
      true,
      basePaint,
    );

    // 创建主体渐变 - 增强背景颜色对比度
    final paint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(0.1, 0.1),
        radius: 0.8,
        colors: const [
          AppTheme_PUPPII.neumorphismLight_PUPPII,
          AppTheme_PUPPII.neumorphismBase_PUPPII,
        ],
        stops: const [0.0, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.fill;

    // 绘制主体扇形 - 从右下角向左上角展开
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      3.14159, // 180度 - 水平向左开始
      1.5708, // 90度扫描角度 - 向上扫描到270度
      true,
      paint,
    );

    // 添加高光边缘
    final highlightPaint = Paint()
      ..color = AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius - 1),
      3.14159, // 180度
      1.5708, // 90度扫描角度
      false,
      highlightPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
