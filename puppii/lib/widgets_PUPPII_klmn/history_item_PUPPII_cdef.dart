import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../models_PUPPII_efgh/chat_session_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/chat_storage_service_PUPPII_cdef.dart';
import '../views_PUPPII_mnop/chat_page_PUPPII_ghij.dart';

class HistoryItem_PUPPII extends StatelessWidget {
  final CharacterModel_PUPPII character_PUPPII;
  final ChatSession_PUPPII? session_PUPPII;
  final bool isLeftAligned_PUPPII;
  final VoidCallback? onDelete_PUPPII;

  const HistoryItem_PUPPII({
    super.key,
    required this.character_PUPPII,
    this.session_PUPPII,
    required this.isLeftAligned_PUPPII,
    this.onDelete_PUPPII,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
      child: Row(
        mainAxisAlignment: isLeftAligned_PUPPII
            ? MainAxisAlignment.start
            : MainAxisAlignment.end,
        children: [
          if (!isLeftAligned_PUPPII) const Spacer(),

          // 药丸形态的历史记录项 - 重新设计布局
          GestureDetector(
            onTap: () {
              // 点击进入聊天页面
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChatPage_PUPPII(
                    character_PUPPII: character_PUPPII,
                  ),
                ),
              );
            },
            child: SizedBox(
              width: 300.w,
              height: 90.h, // 保持80h来容纳头像
              child: Stack(
                children: [
                  // 药丸背景 - 从头像中心开始，高度65h
                  Positioned(
                    left: 45.w, // 头像半径
                    bottom: 0.h, // (80-65)/2，让背景垂直居中
                    right: 0,
                    child: Container(
                      height: 75.h,
                      decoration: BoxDecoration(
                        color: AppTheme_PUPPII.backgroundColor_PUPPII,
                        borderRadius: BorderRadius.circular(32.5.h),
                        boxShadow: [
                          // 外阴影
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            offset: Offset(4.w, 4.h),
                            blurRadius: 8,
                          ),
                          // 内阴影效果
                          BoxShadow(
                            color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                .withOpacity(0.2),
                            offset: Offset(2.w, 2.h),
                            blurRadius: 4,
                          ),
                          // 高光
                          BoxShadow(
                            color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                .withOpacity(0.8),
                            offset: Offset(-2.w, -2.h),
                            blurRadius: 4,
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: 52.w, right: 12.w), // 为头像和删除按钮留空间
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 角色名称
                            Text(
                              character_PUPPII.nickname_PUPPII,
                              style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),

                            SizedBox(height: 4.h),

                            // 最新消息
                            Text(
                              _getLatestMessage(),
                              style:
                                  AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                                fontSize: 11.sp,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),

                            SizedBox(height: 4.h),

                            // 免费次数（如果有剩余）
                            GetX<CharacterStateService_PUPPII>(
                              builder: (controller) {
                                final currentCharacter =
                                    controller.getCharacterById_PUPPII(
                                        character_PUPPII.id_PUPPII);
                                final freeCount =
                                    currentCharacter?.freeChatCount_PUPPII ?? 0;

                                return Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 6.w, vertical: 2.h),
                                  decoration: BoxDecoration(
                                    color: AppTheme_PUPPII.primaryColor_PUPPII
                                        .withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    '$freeCount free',
                                    style:
                                        AppTheme_PUPPII.caption_PUPPII.copyWith(
                                      fontSize: 9.sp,
                                      color:
                                          AppTheme_PUPPII.primaryColor_PUPPII,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 左侧头像 - 80h高度，突出于65h背景
                  Positioned(
                    left: 0,
                    top: 0,
                    child: Container(
                      width: 90.h,
                      height: 90.h,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.15),
                            offset: Offset(2.w, 2.h),
                            blurRadius: 6,
                          ),
                        ],
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          character_PUPPII.avatarPath_PUPPII,
                          width: 80.h,
                          height: 80.h,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 80.h,
                              height: 80.h,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppTheme_PUPPII.primaryColor_PUPPII
                                    .withOpacity(0.1),
                              ),
                              child: Icon(
                                Icons.person,
                                size: 30.sp,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),

                  // 右上角删除按钮
                  Positioned(
                    top: 16.h,
                    right: 10.w,
                    child: GestureDetector(
                      onTap: () => _showDeleteDialog(context),
                      child: SizedBox(
                        width: 24.w,
                        height: 24.w,
                        child: Icon(
                          Icons.close,
                          size: 14.sp,
                          color: Colors.red.withOpacity(0.8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          if (isLeftAligned_PUPPII) const Spacer(),
        ],
      ),
    );
  }

  String _getLatestMessage() {
    if (session_PUPPII == null || session_PUPPII!.messages_PUPPII.isEmpty) {
      return 'No messages yet';
    }

    final latestMessage = session_PUPPII!.messages_PUPPII.last;
    final content = latestMessage.content_PUPPII.trim();

    if (content.isEmpty) {
      return 'Empty message';
    }

    return content.length > 30 ? '${content.substring(0, 30)}...' : content;
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme_PUPPII.backgroundColor_PUPPII,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.w),
        ),
        title: Text(
          'Delete Chat History',
          style: AppTheme_PUPPII.heading3_PUPPII.copyWith(
            fontSize: 16.sp,
          ),
        ),
        content: Text(
          'Are you sure you want to delete the chat history with ${character_PUPPII.nickname_PUPPII}?',
          style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
            fontSize: 12.sp,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: AppTheme_PUPPII.textSecondary_PUPPII,
                fontSize: 12.sp,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // 删除聊天记录
              if (session_PUPPII != null) {
                await ChatStorageService_PUPPII.deleteChatSession_PUPPII(
                  session_PUPPII!.sessionId_PUPPII,
                );
              }

              // 调用删除回调
              onDelete_PUPPII?.call();
            },
            child: Text(
              'Delete',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    ).then((_) {
      // 确保对话框关闭时不让任何输入框获取焦点
      if (context.mounted) {
        FocusScope.of(context).unfocus();
      }
    });
  }
}
