import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../services_PUPPII_uvwx/soul_question_service_PUPPII_abcd.dart';
import '../models_PUPPII_efgh/soul_question_model_PUPPII_wxyz.dart';

class SoulQuestionCard_PUPPII extends StatefulWidget {
  const SoulQuestionCard_PUPPII({super.key});

  @override
  State<SoulQuestionCard_PUPPII> createState() =>
      _SoulQuestionCardState_PUPPII();
}

class _SoulQuestionCardState_PUPPII extends State<SoulQuestionCard_PUPPII> {
  SoulQuestionService_PUPPII? _soulQuestionService_PUPPII;

  SoulQuestionService_PUPPII? get soulQuestionService_PUPPII {
    if (_soulQuestionService_PUPPII == null) {
      try {
        _soulQuestionService_PUPPII = Get.find<SoulQuestionService_PUPPII>();
      } catch (e) {
        print('[SoulQuestionCard] Service not ready: $e');
        return null;
      }
    }
    return _soulQuestionService_PUPPII;
  }

  final TextEditingController _answerController_PUPPII =
      TextEditingController();
  bool _isSubmitting_PUPPII = false;

  @override
  void dispose() {
    _answerController_PUPPII.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final service = soulQuestionService_PUPPII;
    if (service == null) {
      return _buildLoadingCard_PUPPII();
    }

    return Obx(() {
      if (service.isLoading_PUPPII.value) {
        return _buildLoadingCard_PUPPII();
      }

      return Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme_PUPPII.backgroundColor_PUPPII,
              AppTheme_PUPPII.neumorphismBase_PUPPII,
            ],
          ),
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
              offset: Offset(8.w, 8.h),
              blurRadius: 20,
            ),
            BoxShadow(
              color: AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.8),
              offset: Offset(-4.w, -4.h),
              blurRadius: 15,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和图标
            Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.8),
                        AppTheme_PUPPII.primaryColor_PUPPII,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme_PUPPII.primaryColor_PUPPII
                            .withOpacity(0.3),
                        offset: Offset(0, 4.h),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.psychology_outlined,
                    color: Colors.white,
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Soul Question',
                        style: GoogleFonts.comfortaa(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w700,
                          color: AppTheme_PUPPII.textPrimary_PUPPII,
                        ),
                      ),
                      Text(
                        'Daily reflection for 10 coins',
                        style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                          fontSize: 12.sp,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      ),
                    ],
                  ),
                ),
                // 首次使用提示
                if (service.isFirstTimeUser_PUPPII.value)
                  GestureDetector(
                    onTap: _showGuideDialog_PUPPII,
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: AppTheme_PUPPII.primaryColor_PUPPII
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(
                          color: AppTheme_PUPPII.primaryColor_PUPPII
                              .withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.help_outline,
                            size: 14.sp,
                            color: AppTheme_PUPPII.primaryColor_PUPPII,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            'Guide',
                            style:
                                AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                              fontSize: 10.sp,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),

            SizedBox(height: 16.h),

            // 内容区域
            if (!service.canAnswerToday_PUPPII.value)
              _buildCompletedState_PUPPII()
            else
              _buildQuestionState_PUPPII(),
          ],
        ),
      );
    });
  }

  Widget _buildLoadingCard_PUPPII() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      padding: EdgeInsets.all(20.w),
      height: 120.h,
      decoration: AppTheme_PUPPII.neumorphismCard_PUPPII,
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildCompletedState_PUPPII() {
    final service = soulQuestionService_PUPPII;
    if (service == null) return Container();
    final stats = service.getStats_PUPPII();

    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: AppTheme_PUPPII.successColor_PUPPII.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: AppTheme_PUPPII.successColor_PUPPII.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: AppTheme_PUPPII.successColor_PUPPII,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Today\'s question completed!',
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppTheme_PUPPII.successColor_PUPPII,
                      ),
                    ),
                    Text(
                      'Come back tomorrow for a new question',
                      style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                        fontSize: 12.sp,
                        color: AppTheme_PUPPII.textSecondary_PUPPII,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 12.h),

        // 统计信息
        Row(
          children: [
            Expanded(
              child: _buildStatItem_PUPPII(
                'Total Answers',
                '${stats['totalAnswers']}',
                Icons.quiz_outlined,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatItem_PUPPII(
                'Coins Earned',
                '${stats['totalCoinsEarned']}',
                Icons.monetization_on_outlined,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem_PUPPII(String label, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppTheme_PUPPII.backgroundColor_PUPPII,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.2),
            offset: Offset(2.w, 2.h),
            blurRadius: 4,
          ),
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.7),
            offset: Offset(-1.w, -1.h),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 20.sp,
            color: AppTheme_PUPPII.primaryColor_PUPPII,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme_PUPPII.textPrimary_PUPPII,
            ),
          ),
          Text(
            label,
            style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
              fontSize: 10.sp,
              color: AppTheme_PUPPII.textSecondary_PUPPII,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionState_PUPPII() {
    return GestureDetector(
      onTap: _handleQuestionTap_PUPPII,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.05),
              AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.touch_app_outlined,
                  color: AppTheme_PUPPII.primaryColor_PUPPII,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Tap to get today\'s question',
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              'Answer thoughtfully and earn 10 coins',
              style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                fontSize: 12.sp,
                color: AppTheme_PUPPII.textSecondary_PUPPII,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleQuestionTap_PUPPII() async {
    final service = soulQuestionService_PUPPII;
    if (service == null) return;

    if (service.isFirstTimeUser_PUPPII.value) {
      _showGuideDialog_PUPPII();
    } else {
      _showQuestionDialog_PUPPII();
    }
  }

  void _showGuideDialog_PUPPII() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: AppTheme_PUPPII.backgroundColor_PUPPII,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                offset: Offset(8.w, 8.h),
                blurRadius: 20,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 图标
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.8),
                      AppTheme_PUPPII.primaryColor_PUPPII,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(30.r),
                ),
                child: Icon(
                  Icons.psychology_outlined,
                  color: Colors.white,
                  size: 30.sp,
                ),
              ),

              SizedBox(height: 20.h),

              // 标题
              Text(
                'Welcome to Soul Questions!',
                style: GoogleFonts.comfortaa(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme_PUPPII.textPrimary_PUPPII,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 16.h),

              // 规则说明
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppTheme_PUPPII.backgroundColor_PUPPII,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismDark_PUPPII
                          .withOpacity(0.2),
                      offset: Offset(4.w, 4.h),
                      blurRadius: 8,
                    ),
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismLight_PUPPII
                          .withOpacity(0.7),
                      offset: Offset(-2.w, -2.h),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildRuleItem_PUPPII('🎯', 'One question per day'),
                    SizedBox(height: 8.h),
                    _buildRuleItem_PUPPII(
                        '💭', 'Reflect and answer thoughtfully'),
                    SizedBox(height: 8.h),
                    _buildRuleItem_PUPPII(
                        '🪙', 'Earn 10 coins for each answer'),
                    SizedBox(height: 8.h),
                    _buildRuleItem_PUPPII(
                        '🌟', 'Build a habit of self-reflection'),
                  ],
                ),
              ),

              SizedBox(height: 24.h),

              // 按钮
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        height: 48.h,
                        decoration: BoxDecoration(
                          color: AppTheme_PUPPII.backgroundColor_PUPPII,
                          borderRadius: BorderRadius.circular(24.r),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                  .withOpacity(0.3),
                              offset: Offset(4.w, 4.h),
                              blurRadius: 8,
                            ),
                            BoxShadow(
                              color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                  .withOpacity(0.7),
                              offset: Offset(-2.w, -2.h),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            'Maybe Later',
                            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _showQuestionDialog_PUPPII();
                      },
                      child: Container(
                        height: 48.h,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppTheme_PUPPII.primaryColor_PUPPII
                                  .withOpacity(0.8),
                              AppTheme_PUPPII.primaryColor_PUPPII,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(24.r),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme_PUPPII.primaryColor_PUPPII
                                  .withOpacity(0.3),
                              offset: Offset(0, 4.h),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            'Start Now!',
                            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRuleItem_PUPPII(String emoji, String text) {
    return Row(
      children: [
        Text(
          emoji,
          style: TextStyle(fontSize: 16.sp),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Text(
            text,
            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
              fontSize: 14.sp,
              color: AppTheme_PUPPII.textPrimary_PUPPII,
            ),
          ),
        ),
      ],
    );
  }

  void _showQuestionDialog_PUPPII() async {
    final service = soulQuestionService_PUPPII;
    if (service == null) return;

    final question = await service.getTodayQuestion_PUPPII();
    if (question == null) return;

    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildQuestionDialog_PUPPII(question),
    );
  }

  Widget _buildQuestionDialog_PUPPII(SoulQuestionModel_PUPPII question) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: AppTheme_PUPPII.backgroundColor_PUPPII,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
              offset: Offset(8.w, 8.h),
              blurRadius: 20,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Text(
              'Today\'s Soul Question',
              style: GoogleFonts.comfortaa(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme_PUPPII.textPrimary_PUPPII,
              ),
            ),

            SizedBox(height: 20.h),

            // 问题
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.05),
                    AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Text(
                question.question_PUPPII,
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme_PUPPII.textPrimary_PUPPII,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 20.h),

            // 输入框
            Container(
              decoration: BoxDecoration(
                color: AppTheme_PUPPII.backgroundColor_PUPPII,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color:
                        AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.2),
                    offset: Offset(4.w, 4.h),
                    blurRadius: 8,
                  ),
                  BoxShadow(
                    color: AppTheme_PUPPII.neumorphismLight_PUPPII
                        .withOpacity(0.7),
                    offset: Offset(-2.w, -2.h),
                    blurRadius: 8,
                  ),
                ],
              ),
              child: TextField(
                controller: _answerController_PUPPII,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: 'Share your thoughts...',
                  hintStyle: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    color: AppTheme_PUPPII.textSecondary_PUPPII,
                    fontSize: 14.sp,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16.w),
                ),
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 14.sp,
                  color: AppTheme_PUPPII.textPrimary_PUPPII,
                ),
              ),
            ),

            SizedBox(height: 24.h),

            // 按钮
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      _answerController_PUPPII.clear();
                      Navigator.pop(context);
                    },
                    child: Container(
                      height: 48.h,
                      decoration: BoxDecoration(
                        color: AppTheme_PUPPII.backgroundColor_PUPPII,
                        borderRadius: BorderRadius.circular(24.r),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                .withOpacity(0.3),
                            offset: Offset(4.w, 4.h),
                            blurRadius: 8,
                          ),
                          BoxShadow(
                            color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                .withOpacity(0.7),
                            offset: Offset(-2.w, -2.h),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          'Cancel',
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: GestureDetector(
                    onTap: _isSubmitting_PUPPII ? null : _submitAnswer_PUPPII,
                    child: Container(
                      height: 48.h,
                      decoration: BoxDecoration(
                        gradient: _isSubmitting_PUPPII
                            ? null
                            : LinearGradient(
                                colors: [
                                  AppTheme_PUPPII.primaryColor_PUPPII
                                      .withOpacity(0.8),
                                  AppTheme_PUPPII.primaryColor_PUPPII,
                                ],
                              ),
                        color: _isSubmitting_PUPPII
                            ? AppTheme_PUPPII.textSecondary_PUPPII
                            : null,
                        borderRadius: BorderRadius.circular(24.r),
                        boxShadow: _isSubmitting_PUPPII
                            ? null
                            : [
                                BoxShadow(
                                  color: AppTheme_PUPPII.primaryColor_PUPPII
                                      .withOpacity(0.3),
                                  offset: Offset(0, 4.h),
                                  blurRadius: 8,
                                ),
                              ],
                      ),
                      child: Center(
                        child: _isSubmitting_PUPPII
                            ? SizedBox(
                                width: 20.w,
                                height: 20.w,
                                child: const CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Submit',
                                    style: AppTheme_PUPPII.bodyText_PUPPII
                                        .copyWith(
                                      color: Colors.white,
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(width: 8.w),
                                  Text(
                                    '+10🪙',
                                    style: AppTheme_PUPPII.bodyText_PUPPII
                                        .copyWith(
                                      color: Colors.white,
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _submitAnswer_PUPPII() async {
    final answer = _answerController_PUPPII.text.trim();
    if (answer.isEmpty) {
      Get.snackbar(
        'Empty Answer',
        'Please write your thoughts before submitting',
        backgroundColor: AppTheme_PUPPII.errorColor_PUPPII.withOpacity(0.1),
        colorText: AppTheme_PUPPII.errorColor_PUPPII,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    final service = soulQuestionService_PUPPII;
    if (service == null) return;

    setState(() => _isSubmitting_PUPPII = true);

    final success = await service.submitAnswer_PUPPII(answer);

    if (!mounted) return;
    setState(() => _isSubmitting_PUPPII = false);

    if (success) {
      _answerController_PUPPII.clear();
      Navigator.pop(context);

      Get.snackbar(
        'Answer Submitted!',
        'You earned 10 coins for your thoughtful reflection',
        backgroundColor: AppTheme_PUPPII.successColor_PUPPII.withOpacity(0.1),
        colorText: AppTheme_PUPPII.successColor_PUPPII,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } else {
      Get.snackbar(
        'Submission Failed',
        'Please try again later',
        backgroundColor: AppTheme_PUPPII.errorColor_PUPPII.withOpacity(0.1),
        colorText: AppTheme_PUPPII.errorColor_PUPPII,
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
