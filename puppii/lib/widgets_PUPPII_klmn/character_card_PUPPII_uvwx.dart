import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';

class CharacterCard_PUPPII extends StatelessWidget {
  final CharacterModel_PUPPII character_PUPPII;
  final VoidCallback? onTap_PUPPII;

  const CharacterCard_PUPPII({
    super.key,
    required this.character_PUPPII,
    this.onTap_PUPPII,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap_PUPPII,
      child: Container(
        width: 220.w,
        decoration: AppTheme_PUPPII.neumorphismCard_PUPPII,
        child: Padding(
          padding: EdgeInsets.all(14.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 头像
              Container(
                width: 120.w,
                height: 120.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    // 右下方向的深色阴影
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      offset: Offset(4.w, 4.h),
                      blurRadius: 8,
                    ),
                    // 温暖色调的阴影
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismDark_PUPPII
                          .withOpacity(0.3),
                      offset: Offset(2.w, 2.h),
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: ClipOval(
                  child: Image.asset(
                    character_PUPPII.avatarPath_PUPPII,
                    width: 120.w,
                    height: 120.w,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80.w,
                        height: 80.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppTheme_PUPPII.primaryColor_PUPPII
                              .withOpacity(0.1),
                        ),
                        child: Icon(
                          Icons.person,
                          size: 30.sp,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      );
                    },
                  ),
                ),
              ),

              SizedBox(height: 8.h),

              // 昵称
              Text(
                character_PUPPII.nickname_PUPPII,
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 6.h),

              // 短介绍
              Text(
                character_PUPPII.shortIntroduction_PUPPII,
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme_PUPPII.primaryColor_PUPPII,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 6.h),

              // 免费聊天次数 - 使用GetX监听状态变化
              GetX<CharacterStateService_PUPPII>(
                builder: (controller) {
                  final currentCharacter = controller
                      .getCharacterById_PUPPII(character_PUPPII.id_PUPPII);
                  final freeCount = currentCharacter?.freeChatCount_PUPPII ??
                      character_PUPPII.freeChatCount_PUPPII;

                  return Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: freeCount > 0
                          ? AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1)
                          : AppTheme_PUPPII.textSecondary_PUPPII
                              .withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      freeCount > 0 ? '$freeCount free' : 'No free chats',
                      style: AppTheme_PUPPII.caption_PUPPII.copyWith(
                        fontSize: 11.sp,
                        color: freeCount > 0
                            ? AppTheme_PUPPII.primaryColor_PUPPII
                            : AppTheme_PUPPII.textSecondary_PUPPII,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
