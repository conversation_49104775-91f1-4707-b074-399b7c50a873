import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../views_PUPPII_mnop/chat_page_PUPPII_ghij.dart';
import 'character_card_PUPPII_uvwx.dart';

class CharacterListItem_PUPPII extends StatelessWidget {
  final CharacterModel_PUPPII character_PUPPII;
  final bool isLeftAligned_PUPPII; // true: 卡片在左，介绍在右；false: 卡片在右，介绍在左
  final VoidCallback? onTap_PUPPII;

  const CharacterListItem_PUPPII({
    super.key,
    required this.character_PUPPII,
    required this.isLeftAligned_PUPPII,
    this.onTap_PUPPII,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: isLeftAligned_PUPPII
            ? _buildLeftAlignedLayout()
            : _buildRightAlignedLayout(),
      ),
    );
  }

  List<Widget> _buildLeftAlignedLayout() {
    return [
      // 左侧：角色卡片
      CharacterCard_PUPPII(
        character_PUPPII: character_PUPPII,
        onTap_PUPPII: onTap_PUPPII,
      ),

      SizedBox(width: 16.w),

      // 右侧：介绍信息
      Expanded(
        child: _buildIntroductionSection(),
      ),
    ];
  }

  List<Widget> _buildRightAlignedLayout() {
    return [
      // 左侧：介绍信息
      Expanded(
        child: _buildIntroductionSection(),
      ),

      SizedBox(width: 16.w),

      // 右侧：角色卡片
      CharacterCard_PUPPII(
        character_PUPPII: character_PUPPII,
        onTap_PUPPII: onTap_PUPPII,
      ),
    ];
  }

  Widget _buildIntroductionSection() {
    return Column(
      crossAxisAlignment: isLeftAligned_PUPPII
          ? CrossAxisAlignment.start
          : CrossAxisAlignment.end,
      children: [
        // 上部分：文本介绍
        Column(
          crossAxisAlignment: isLeftAligned_PUPPII
              ? CrossAxisAlignment.start
              : CrossAxisAlignment.end,
          children: [
            // 短介绍
            Text(
              character_PUPPII.shortIntroduction_PUPPII,
              style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppTheme_PUPPII.primaryColor_PUPPII,
              ),
              textAlign:
                  isLeftAligned_PUPPII ? TextAlign.left : TextAlign.right,
            ),

            SizedBox(height: 8.h),

            // 长介绍
            Text(
              character_PUPPII.longIntroduction_PUPPII,
              style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                fontSize: 12.sp,
                height: 1.4,
              ),
              textAlign:
                  isLeftAligned_PUPPII ? TextAlign.left : TextAlign.right,
              maxLines: 6,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // 下部分：聊天按钮
        _buildChatButton(),
      ],
    );
  }

  Widget _buildChatButton() {
    return Builder(
      builder: (context) => GestureDetector(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ChatPage_PUPPII(
                character_PUPPII: character_PUPPII,
              ),
            ),
          );
        },
        child: Container(
          width: 120.w,
          height: 36.h,
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(18),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 16.sp,
                  color: AppTheme_PUPPII.primaryColor_PUPPII,
                ),
                SizedBox(width: 6.w),
                Text(
                  'Chat',
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
