import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import '../views_PUPPII_mnop/chat_page_PUPPII_ghij.dart';

class FavoriteCharacterCard_PUPPII extends StatelessWidget {
  final CharacterModel_PUPPII character_PUPPII;
  final VoidCallback? onUnfavorite_PUPPII;

  const FavoriteCharacterCard_PUPPII({
    super.key,
    required this.character_PUPPII,
    this.onUnfavorite_PUPPII,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
      child: Stack(
        children: [
          // 主卡片
          GestureDetector(
            onTap: () {
              // 跳转到聊天页面
              Get.to(() => ChatPage_PUPPII(character_PUPPII: character_PUPPII));
            },
            child: Container(
              width: 140.w,
              height: 180.h,
              decoration: BoxDecoration(
                color: AppTheme_PUPPII.backgroundColor_PUPPII,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  // 外阴影 - 深色
                  BoxShadow(
                    color:
                        AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                    offset: Offset(6.w, 6.h),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                  // 内阴影效果 - 浅色高光
                  BoxShadow(
                    color: AppTheme_PUPPII.neumorphismLight_PUPPII
                        .withOpacity(0.8),
                    offset: Offset(-4.w, -4.h),
                    blurRadius: 8,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 头像
                    Container(
                      width: 80.w,
                      height: 80.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          // 头像阴影
                          BoxShadow(
                            color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                .withOpacity(0.4),
                            offset: Offset(3.w, 3.h),
                            blurRadius: 8,
                            spreadRadius: 0,
                          ),
                          // 头像高光
                          BoxShadow(
                            color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                .withOpacity(0.9),
                            offset: Offset(-2.w, -2.h),
                            blurRadius: 6,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          character_PUPPII.avatarPath_PUPPII,
                          width: 80.w,
                          height: 80.w,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppTheme_PUPPII.primaryColor_PUPPII
                                    .withOpacity(0.1),
                              ),
                              child: Icon(
                                Icons.person,
                                size: 40.sp,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    SizedBox(height: 16.h),

                    // 昵称
                    Text(
                      character_PUPPII.nickname_PUPPII,
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppTheme_PUPPII.textPrimary_PUPPII,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // 取消收藏按钮
          Positioned(
            top: 8.h,
            right: 8.w,
            child: GestureDetector(
              onTap: () async {
                final userService = Get.find<UserService_PUPPII>();
                final success =
                    await userService.removeFavoriteCharacter_PUPPII(
                  character_PUPPII.id_PUPPII,
                );

                if (success && onUnfavorite_PUPPII != null) {
                  onUnfavorite_PUPPII!();
                }
              },
              child: Container(
                width: 28.w,
                height: 28.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme_PUPPII.backgroundColor_PUPPII,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismDark_PUPPII
                          .withOpacity(0.3),
                      offset: Offset(2.w, 2.h),
                      blurRadius: 4,
                    ),
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismLight_PUPPII
                          .withOpacity(0.8),
                      offset: Offset(-1.w, -1.h),
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.favorite,
                  size: 16.sp,
                  color: Colors.red,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
