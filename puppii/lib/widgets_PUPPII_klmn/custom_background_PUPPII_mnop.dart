import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';

class CustomBackground_PUPPII extends StatelessWidget {
  final Widget child;

  const CustomBackground_PUPPII({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme_PUPPII.backgroundColor_PUPPII,
            AppTheme_PUPPII.backgroundColor_PUPPII.withOpacity(0.95),
            AppTheme_PUPPII.neumorphismBase_PUPPII.withOpacity(0.3),
          ],
        ),
      ),
      child: Stack(
        children: [
          // 背景线条装饰
          CustomPaint(
            painter: BackgroundLinesPainter_PUPPII(),
            size: Size.infinite,
          ),
          // 右上角图标
          Positioned(
            top: 80.h,
            right: 30.w,
            child: Image.asset(
              'assets_puppii/icons_puppii/b1.png',
              width: 50.w,
              height: 50.w,
              color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.3),
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.w),
                  ),
                  child: Icon(
                    Icons.star,
                    size: 20.sp,
                    color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.3),
                  ),
                );
              },
            ),
          ),
          // 左下角图标
          Positioned(
            bottom: 120.h,
            left: 30.w,
            child: Image.asset(
              'assets_puppii/icons_puppii/b2.png',
              width: 50.w,
              height: 50.w,
              color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.25),
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 35.w,
                  height: 35.w,
                  decoration: BoxDecoration(
                    color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6.w),
                  ),
                  child: Icon(
                    Icons.favorite,
                    size: 18.sp,
                    color:
                        AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.25),
                  ),
                );
              },
            ),
          ),
          // 主要内容
          child,
        ],
      ),
    );
  }
}

class BackgroundLinesPainter_PUPPII extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.08)
      ..strokeWidth = 1.5.w
      ..style = PaintingStyle.stroke;

    // 左上角装饰线条
    _drawTopLeftLines(canvas, size, paint);

    // 右下角装饰线条
    _drawBottomRightLines(canvas, size, paint);

    // 中间的几何装饰
    _drawCenterGeometry(canvas, size, paint);
  }

  void _drawTopLeftLines(Canvas canvas, Size size, Paint paint) {
    final path = Path();

    // 弧形线条1
    path.moveTo(0, size.height * 0.15);
    path.quadraticBezierTo(size.width * 0.2, size.height * 0.1,
        size.width * 0.4, size.height * 0.12);
    canvas.drawPath(path, paint);

    // 弧形线条2
    final path2 = Path();
    path2.moveTo(0, size.height * 0.25);
    path2.quadraticBezierTo(size.width * 0.15, size.height * 0.18,
        size.width * 0.35, size.height * 0.22);
    canvas.drawPath(path2, paint);

    // 垂直装饰线
    canvas.drawLine(
      Offset(size.width * 0.08, size.height * 0.05),
      Offset(size.width * 0.08, size.height * 0.3),
      paint,
    );
  }

  void _drawBottomRightLines(Canvas canvas, Size size, Paint paint) {
    final path = Path();

    // 弧形线条1
    path.moveTo(size.width, size.height * 0.75);
    path.quadraticBezierTo(size.width * 0.8, size.height * 0.8,
        size.width * 0.6, size.height * 0.78);
    canvas.drawPath(path, paint);

    // 弧形线条2
    final path2 = Path();
    path2.moveTo(size.width, size.height * 0.85);
    path2.quadraticBezierTo(size.width * 0.85, size.height * 0.9,
        size.width * 0.65, size.height * 0.88);
    canvas.drawPath(path2, paint);

    // 垂直装饰线
    canvas.drawLine(
      Offset(size.width * 0.92, size.height * 0.7),
      Offset(size.width * 0.92, size.height * 0.95),
      paint,
    );
  }

  void _drawCenterGeometry(Canvas canvas, Size size, Paint paint) {
    // 中心圆形装饰
    final centerX = size.width * 0.5;
    final centerY = size.height * 0.45;

    // 外圆
    canvas.drawCircle(
      Offset(centerX, centerY),
      80.w,
      paint..color = AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.05),
    );

    // 内圆
    canvas.drawCircle(
      Offset(centerX, centerY),
      50.w,
      paint..color = AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.03),
    );

    // 几何连接线
    final geometryPaint = Paint()
      ..color = AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.06)
      ..strokeWidth = 1.w
      ..style = PaintingStyle.stroke;

    // 连接线1 - 从左上到中心
    canvas.drawLine(
      Offset(size.width * 0.2, size.height * 0.2),
      Offset(centerX - 30.w, centerY - 30.h),
      geometryPaint,
    );

    // 连接线2 - 从右下到中心
    canvas.drawLine(
      Offset(size.width * 0.8, size.height * 0.7),
      Offset(centerX + 30.w, centerY + 30.h),
      geometryPaint,
    );

    // 小装饰点
    final dotPaint = Paint()
      ..color = AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
        Offset(size.width * 0.15, size.height * 0.3), 3.w, dotPaint);
    canvas.drawCircle(
        Offset(size.width * 0.85, size.height * 0.6), 3.w, dotPaint);
    canvas.drawCircle(
        Offset(size.width * 0.3, size.height * 0.8), 2.w, dotPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
