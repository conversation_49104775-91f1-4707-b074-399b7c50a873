import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme_PUPPII {
  // Primary Colors - Warm Craft Theme
  static const Color primaryColor_PUPPII = Color(0xFFD2691E); // Chocolate - 主色调
  static const Color secondaryColor_PUPPII =
      Color(0xFF228B22); // Forest Green - 辅助色
  static const Color accentColor_PUPPII = Color(0xFF4169E1); // Royal Blue - 强调色
  static const Color backgroundColor_PUPPII = Color(0xFFF0E6D6); // 更明显的温暖米色背景
  static const Color surfaceColor_PUPPII = Color(0xFFFFFFFF); // White - 表面色

  // Text Colors
  static const Color textPrimary_PUPPII = Color(0xFF2F2F2F); // Dark Gray - 主要文字
  static const Color textSecondary_PUPPII =
      Color(0xFF666666); // Medium Gray - 次要文字
  static const Color textLight_PUPPII = Color(0xFF999999); // Light Gray - 浅色文字

  // Status Colors
  static const Color successColor_PUPPII = Color(0xFF4CAF50); // Green - 成功
  static const Color warningColor_PUPPII = Color(0xFFFF9800); // Orange - 警告
  static const Color errorColor_PUPPII = Color(0xFFF44336); // Red - 错误
  static const Color infoColor_PUPPII = Color(0xFF2196F3); // Blue - 信息

  // Gradient Colors
  static const LinearGradient primaryGradient_PUPPII = LinearGradient(
    colors: [Color(0xFFD2691E), Color(0xFFCD853F)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient_PUPPII = LinearGradient(
    colors: [Color(0xFF228B22), Color(0xFF32CD32)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Neumorphism Colors - 新拟物化颜色 (温暖色调)
  static const Color neumorphismLight_PUPPII = Color(0xFFFFFBF5); // 温暖高光色
  static const Color neumorphismDark_PUPPII = Color(0xFFE8D5C4); // 温暖阴影色
  static const Color neumorphismBase_PUPPII = Color(0xFFF5F0E8); // 温暖基础色

  // Neumorphism Decorations - 新拟物化装饰
  static BoxDecoration get neumorphismElevated_PUPPII => BoxDecoration(
        color: neumorphismBase_PUPPII,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: neumorphismDark_PUPPII,
            offset: const Offset(6, 6),
            blurRadius: 12,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: neumorphismLight_PUPPII,
            offset: const Offset(-6, -6),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      );

  // 卡片专用的增强拟物化阴影效果
  static BoxDecoration get neumorphismCard_PUPPII => BoxDecoration(
        color: neumorphismBase_PUPPII,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          // 右下方向的深色阴影（增强立体感）
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            offset: const Offset(6, 6),
            blurRadius: 12,
            spreadRadius: 0,
          ),
          // 温暖色调的阴影
          BoxShadow(
            color: neumorphismDark_PUPPII.withOpacity(0.3),
            offset: const Offset(4, 4),
            blurRadius: 8,
            spreadRadius: 0,
          ),
          // 左上方向的高光
          BoxShadow(
            color: neumorphismLight_PUPPII.withOpacity(0.9),
            offset: const Offset(-4, -4),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      );

  static BoxDecoration get neumorphismPressed_PUPPII => BoxDecoration(
        color: neumorphismBase_PUPPII.withOpacity(0.8),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: neumorphismDark_PUPPII.withOpacity(0.5),
            offset: const Offset(2, 2),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      );

  // Text Styles
  static TextStyle get heading1_PUPPII => GoogleFonts.poppins(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: textPrimary_PUPPII,
      );

  static TextStyle get heading2_PUPPII => GoogleFonts.poppins(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: textPrimary_PUPPII,
      );

  static TextStyle get heading3_PUPPII => GoogleFonts.poppins(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: textPrimary_PUPPII,
      );

  static TextStyle get bodyText_PUPPII => GoogleFonts.poppins(
        fontSize: 16,
        fontWeight: FontWeight.normal,
        color: textPrimary_PUPPII,
      );

  static TextStyle get bodyTextSmall_PUPPII => GoogleFonts.poppins(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: textSecondary_PUPPII,
      );

  static TextStyle get caption_PUPPII => GoogleFonts.poppins(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        color: textLight_PUPPII,
      );

  // Button Styles
  static ButtonStyle get primaryButton_PUPPII => ElevatedButton.styleFrom(
        backgroundColor: primaryColor_PUPPII,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      );

  static ButtonStyle get secondaryButton_PUPPII => OutlinedButton.styleFrom(
        foregroundColor: primaryColor_PUPPII,
        side: const BorderSide(color: primaryColor_PUPPII, width: 2),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      );

  // Card Style
  static BoxDecoration get cardDecoration_PUPPII => BoxDecoration(
        color: surfaceColor_PUPPII,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      );

  // Input Decoration
  static InputDecoration get inputDecoration_PUPPII => InputDecoration(
        filled: true,
        fillColor: surfaceColor_PUPPII,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: textLight_PUPPII),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: textLight_PUPPII),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor_PUPPII, width: 2),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      );

  // Theme Data
  static ThemeData get lightTheme_PUPPII => ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: primaryColor_PUPPII,
          brightness: Brightness.light,
          primary: primaryColor_PUPPII,
          secondary: secondaryColor_PUPPII,
          surface: surfaceColor_PUPPII,
          error: errorColor_PUPPII,
        ),
        textTheme: GoogleFonts.poppinsTextTheme(),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: primaryButton_PUPPII,
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: secondaryButton_PUPPII,
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: surfaceColor_PUPPII,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: textLight_PUPPII),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: textLight_PUPPII),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: primaryColor_PUPPII, width: 2),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        appBarTheme: AppBarTheme(
          backgroundColor: surfaceColor_PUPPII,
          foregroundColor: textPrimary_PUPPII,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: textPrimary_PUPPII,
          ),
        ),
        scaffoldBackgroundColor: backgroundColor_PUPPII,
      );
}
