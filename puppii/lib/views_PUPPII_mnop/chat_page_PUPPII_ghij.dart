import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'dart:math';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../models_PUPPII_efgh/chat_message_PUPPII_mnop.dart';
import '../models_PUPPII_efgh/chat_session_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/doubao_api_service_PUPPII_yzab.dart';
import '../services_PUPPII_uvwx/chat_storage_service_PUPPII_cdef.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import 'store_page_PUPPII_cdef.dart';

class ChatPage_PUPPII extends StatefulWidget {
  final CharacterModel_PUPPII character_PUPPII;

  const ChatPage_PUPPII({
    super.key,
    required this.character_PUPPII,
  });

  @override
  State<ChatPage_PUPPII> createState() => _ChatPageState_PUPPII();
}

class _ChatPageState_PUPPII extends State<ChatPage_PUPPII> {
  final TextEditingController _messageController_PUPPII =
      TextEditingController();
  final ScrollController _scrollController_PUPPII = ScrollController();
  final DoubaoApiService_PUPPII _apiService_PUPPII = DoubaoApiService_PUPPII();
  final CharacterStateService_PUPPII _characterService_PUPPII =
      Get.find<CharacterStateService_PUPPII>();

  ChatSession_PUPPII? _currentSession_PUPPII;
  final UserService_PUPPII _userService_PUPPII = Get.find<UserService_PUPPII>();
  bool _isLoading_PUPPII = false;
  bool _isSending_PUPPII = false;
  bool _isExpanded_PUPPII = false;
  String? _typingMessageId_PUPPII;

  @override
  void initState() {
    super.initState();
    _initializeChat_PUPPII();
  }

  @override
  void dispose() {
    _messageController_PUPPII.dispose();
    _scrollController_PUPPII.dispose();
    super.dispose();
  }

  Future<void> _initializeChat_PUPPII() async {
    setState(() => _isLoading_PUPPII = true);

    try {
      // 获取或创建聊天会话
      _currentSession_PUPPII =
          await ChatStorageService_PUPPII.getChatSessionByCharacter_PUPPII(
        widget.character_PUPPII.id_PUPPII,
      );

      if (_currentSession_PUPPII == null) {
        // 创建新会话
        _currentSession_PUPPII = ChatSession_PUPPII(
          sessionId_PUPPII: _generateSessionId_PUPPII(),
          character_PUPPII: widget.character_PUPPII,
          messages_PUPPII: [],
          lastUpdated_PUPPII: DateTime.now(),
          remainingChats_PUPPII: widget.character_PUPPII.freeChatCount_PUPPII,
        );
        await ChatStorageService_PUPPII.saveChatSession_PUPPII(
            _currentSession_PUPPII!);
      }
    } catch (e) {
      print('[ChatPage] Error initializing chat: $e');
    } finally {
      setState(() => _isLoading_PUPPII = false);
      // 初始化完成后滚动到底部
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom_PUPPII();
      });
    }
  }

  String _generateSessionId_PUPPII() {
    return '${widget.character_PUPPII.id_PUPPII}_${DateTime.now().millisecondsSinceEpoch}';
  }

  String _generateMessageId_PUPPII() {
    return '${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  Future<void> _sendMessage_PUPPII() async {
    final messageText = _messageController_PUPPII.text.trim();
    if (messageText.isEmpty ||
        _isSending_PUPPII ||
        _currentSession_PUPPII == null) return;

    // 检查角色状态服务中的免费次数
    final currentCharacter = _characterService_PUPPII
        .getCharacterById_PUPPII(widget.character_PUPPII.id_PUPPII);
    final actualFreeCount = currentCharacter?.freeChatCount_PUPPII ?? 0;

    if (actualFreeCount <= 0) {
      _showNoChatsDialog_PUPPII();
      return;
    }

    setState(() => _isSending_PUPPII = true);
    _messageController_PUPPII.clear();

    // 创建用户消息
    final userMessage = ChatMessage_PUPPII(
      id_PUPPII: _generateMessageId_PUPPII(),
      content_PUPPII: messageText,
      isUser_PUPPII: true,
      timestamp_PUPPII: DateTime.now(),
      status_PUPPII: MessageStatus_PUPPII.sent,
    );

    // 添加用户消息到会话
    await ChatStorageService_PUPPII.addMessageToSession_PUPPII(
      _currentSession_PUPPII!.sessionId_PUPPII,
      userMessage,
    );

    // 更新剩余聊天次数
    final newRemainingChats = _currentSession_PUPPII!.remainingChats_PUPPII - 1;
    await ChatStorageService_PUPPII.updateRemainingChats_PUPPII(
      _currentSession_PUPPII!.sessionId_PUPPII,
      newRemainingChats,
    );

    // 刷新会话数据
    _currentSession_PUPPII =
        await ChatStorageService_PUPPII.getChatSessionByCharacter_PUPPII(
      widget.character_PUPPII.id_PUPPII,
    );

    // 立即通知历史页面更新（用户消息已添加）
    Get.find<CharacterStateService_PUPPII>().notifyHistoryUpdate_PUPPII();

    setState(() {});
    _scrollToBottom_PUPPII();

    // 创建AI回复消息（loading状态）
    final aiMessageId = _generateMessageId_PUPPII();
    final aiMessage = ChatMessage_PUPPII(
      id_PUPPII: aiMessageId,
      content_PUPPII: '',
      isUser_PUPPII: false,
      timestamp_PUPPII: DateTime.now(),
      status_PUPPII: MessageStatus_PUPPII.typing,
    );

    await ChatStorageService_PUPPII.addMessageToSession_PUPPII(
      _currentSession_PUPPII!.sessionId_PUPPII,
      aiMessage,
    );

    setState(() => _typingMessageId_PUPPII = aiMessageId);
    _scrollToBottom_PUPPII();

    try {
      // 构建对话历史
      final conversationHistory = _currentSession_PUPPII!.messages_PUPPII
          .where((msg) => msg.status_PUPPII == MessageStatus_PUPPII.sent)
          .map((msg) => {
                'role': msg.isUser_PUPPII ? 'user' : 'assistant',
                'content': msg.content_PUPPII,
              })
          .toList();

      // 调用API获取回复
      final response = await _apiService_PUPPII.sendMessage_PUPPII(
        userMessage: messageText,
        character: widget.character_PUPPII,
        conversationHistory: conversationHistory,
      );

      // 打字机效果显示回复
      await _typewriterEffect_PUPPII(aiMessageId, response);

      // 成功发送消息后，减少角色的免费次数
      await _characterService_PUPPII.decrementCharacterFreeCount_PUPPII(
          widget.character_PUPPII.id_PUPPII);
    } catch (e) {
      print('[ChatPage] Error sending message: $e');
      // 更新消息状态为失败
      await ChatStorageService_PUPPII.updateMessageStatus_PUPPII(
        _currentSession_PUPPII!.sessionId_PUPPII,
        aiMessageId,
        MessageStatus_PUPPII.failed,
        content: 'Sorry, I encountered an error. Please try again.',
      );
    } finally {
      setState(() {
        _isSending_PUPPII = false;
        _typingMessageId_PUPPII = null;
      });
      _refreshSession_PUPPII();
    }
  }

  Future<void> _typewriterEffect_PUPPII(
      String messageId, String fullText) async {
    const int delay = 10; // 每个字符的延迟时间（毫秒）- 加快速度

    for (int i = 0; i <= fullText.length; i++) {
      if (_typingMessageId_PUPPII != messageId) break; // 如果消息ID改变，停止打字机效果

      final partialText = fullText.substring(0, i);
      await ChatStorageService_PUPPII.updateMessageStatus_PUPPII(
        _currentSession_PUPPII!.sessionId_PUPPII,
        messageId,
        i == fullText.length
            ? MessageStatus_PUPPII.sent
            : MessageStatus_PUPPII.typing,
        content: partialText,
      );

      _refreshSession_PUPPII();

      // 减少滚动频率，避免影响用户滚动
      if (i % 5 == 0 || i == fullText.length) {
        _scrollToBottom_PUPPII();
      }

      if (i < fullText.length) {
        await Future.delayed(const Duration(milliseconds: delay));
      }
    }

    // 打字机效果完成后，通知历史页面更新
    if (_typingMessageId_PUPPII == messageId) {
      Get.find<CharacterStateService_PUPPII>().notifyHistoryUpdate_PUPPII();
    }
  }

  Future<void> _refreshSession_PUPPII() async {
    _currentSession_PUPPII =
        await ChatStorageService_PUPPII.getChatSessionByCharacter_PUPPII(
      widget.character_PUPPII.id_PUPPII,
    );
    setState(() {});
  }

  void _scrollToBottom_PUPPII() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController_PUPPII.hasClients) {
        _scrollController_PUPPII.animateTo(
          _scrollController_PUPPII.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _toggleFavorite_PUPPII() async {
    final userService = Get.find<UserService_PUPPII>();
    final characterId = widget.character_PUPPII.id_PUPPII;

    final isFavorite = userService.isFavoriteCharacter_PUPPII(characterId);
    print(
        '[ChatPage] Toggle favorite for $characterId, currently: $isFavorite');

    bool success;
    if (isFavorite) {
      success = await userService.removeFavoriteCharacter_PUPPII(characterId);
    } else {
      success = await userService.addFavoriteCharacter_PUPPII(characterId);
      if (!success) {
        Get.snackbar(
          'Cannot Add to Favorites',
          'You have reached the maximum number of favorite characters',
          backgroundColor: AppTheme_PUPPII.neumorphismBase_PUPPII,
          colorText: AppTheme_PUPPII.textPrimary_PUPPII,
          duration: const Duration(seconds: 2),
        );
      }
    }

    if (success) {
      setState(() {}); // 刷新UI
    }
  }

  void _showNoChatsDialog_PUPPII() {
    final currentCoins = _userService_PUPPII.coinBalance;
    const costPerMessage = 100;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: AppTheme_PUPPII.backgroundColor_PUPPII,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                offset: Offset(8.w, 8.h),
                blurRadius: 20,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                'No Free Chats Left',
                style: AppTheme_PUPPII.heading3_PUPPII.copyWith(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),

              SizedBox(height: 16.h),

              // 内容
              Text(
                'You have used all your free chats with ${widget.character_PUPPII.nickname_PUPPII}.',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 14.sp,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 20.h),

              // 金币信息
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppTheme_PUPPII.backgroundColor_PUPPII,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismDark_PUPPII
                          .withOpacity(0.2),
                      offset: Offset(4.w, 4.h),
                      blurRadius: 8,
                    ),
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismLight_PUPPII
                          .withOpacity(0.7),
                      offset: Offset(-2.w, -2.h),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Cost per message:',
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                          ),
                        ),
                        Text(
                          '$costPerMessage coins',
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: AppTheme_PUPPII.primaryColor_PUPPII,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Your balance:',
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                          ),
                        ),
                        Text(
                          '$currentCoins coins',
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: currentCoins >= costPerMessage
                                ? AppTheme_PUPPII.successColor_PUPPII
                                : AppTheme_PUPPII.errorColor_PUPPII,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              SizedBox(height: 24.h),

              // 按钮
              if (currentCoins >= costPerMessage) ...[
                // 有足够金币，显示支付按钮
                Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          height: 48.h,
                          decoration: BoxDecoration(
                            color: AppTheme_PUPPII.backgroundColor_PUPPII,
                            borderRadius: BorderRadius.circular(24.r),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                    .withOpacity(0.3),
                                offset: Offset(4.w, 4.h),
                                blurRadius: 8,
                              ),
                              BoxShadow(
                                color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                    .withOpacity(0.7),
                                offset: Offset(-2.w, -2.h),
                                blurRadius: 8,
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              'Cancel',
                              style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(width: 16.w),

                    // 支付按钮
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                          _sendMessageWithCoins_PUPPII();
                        },
                        child: Container(
                          height: 48.h,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppTheme_PUPPII.primaryColor_PUPPII
                                    .withOpacity(0.8),
                                AppTheme_PUPPII.primaryColor_PUPPII,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(24.r),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme_PUPPII.primaryColor_PUPPII
                                    .withOpacity(0.3),
                                offset: Offset(0, 4.h),
                                blurRadius: 8,
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              'Pay $costPerMessage Coins',
                              style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                color: Colors.white,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                // 金币不足，显示去商店按钮
                Column(
                  children: [
                    Text(
                      'Insufficient coins! Go to store to purchase more.',
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        fontSize: 14.sp,
                        color: AppTheme_PUPPII.errorColor_PUPPII,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16.h),
                    Row(
                      children: [
                        // 取消按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Container(
                              height: 48.h,
                              decoration: BoxDecoration(
                                color: AppTheme_PUPPII.backgroundColor_PUPPII,
                                borderRadius: BorderRadius.circular(24.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme_PUPPII
                                        .neumorphismDark_PUPPII
                                        .withOpacity(0.3),
                                    offset: Offset(4.w, 4.h),
                                    blurRadius: 8,
                                  ),
                                  BoxShadow(
                                    color: AppTheme_PUPPII
                                        .neumorphismLight_PUPPII
                                        .withOpacity(0.7),
                                    offset: Offset(-2.w, -2.h),
                                    blurRadius: 8,
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  'Cancel',
                                  style:
                                      AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 16.w),

                        // 去商店按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const StorePage_PUPPII_cdef(),
                                ),
                              );
                            },
                            child: Container(
                              height: 48.h,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppTheme_PUPPII.primaryColor_PUPPII
                                        .withOpacity(0.8),
                                    AppTheme_PUPPII.primaryColor_PUPPII,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(24.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme_PUPPII.primaryColor_PUPPII
                                        .withOpacity(0.3),
                                    offset: Offset(0, 4.h),
                                    blurRadius: 8,
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  'Go to Store',
                                  style:
                                      AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                    color: Colors.white,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // 使用金币发送消息
  Future<void> _sendMessageWithCoins_PUPPII() async {
    final messageText = _messageController_PUPPII.text.trim();
    if (messageText.isEmpty ||
        _isSending_PUPPII ||
        _currentSession_PUPPII == null) return;

    const costPerMessage = 100;
    final currentCoins = _userService_PUPPII.coinBalance;

    if (currentCoins < costPerMessage) {
      // 金币不足，不应该到这里，但为了安全起见
      return;
    }

    setState(() => _isSending_PUPPII = true);
    _messageController_PUPPII.clear();

    // 创建用户消息
    final userMessage = ChatMessage_PUPPII(
      id_PUPPII: _generateMessageId_PUPPII(),
      content_PUPPII: messageText,
      isUser_PUPPII: true,
      timestamp_PUPPII: DateTime.now(),
      status_PUPPII: MessageStatus_PUPPII.sent,
    );

    // 添加用户消息到会话
    await ChatStorageService_PUPPII.addMessageToSession_PUPPII(
      _currentSession_PUPPII!.sessionId_PUPPII,
      userMessage,
    );

    // 刷新会话数据
    _currentSession_PUPPII =
        await ChatStorageService_PUPPII.getChatSessionByCharacter_PUPPII(
      widget.character_PUPPII.id_PUPPII,
    );

    // 立即通知历史页面更新（用户消息已添加）
    Get.find<CharacterStateService_PUPPII>().notifyHistoryUpdate_PUPPII();

    setState(() {});
    _scrollToBottom_PUPPII();

    // 创建AI回复消息（loading状态）
    final aiMessageId = _generateMessageId_PUPPII();
    final aiMessage = ChatMessage_PUPPII(
      id_PUPPII: aiMessageId,
      content_PUPPII: '',
      isUser_PUPPII: false,
      timestamp_PUPPII: DateTime.now(),
      status_PUPPII: MessageStatus_PUPPII.typing,
    );

    await ChatStorageService_PUPPII.addMessageToSession_PUPPII(
      _currentSession_PUPPII!.sessionId_PUPPII,
      aiMessage,
    );

    setState(() => _typingMessageId_PUPPII = aiMessageId);
    _scrollToBottom_PUPPII();

    try {
      // 构建对话历史
      final conversationHistory = _currentSession_PUPPII!.messages_PUPPII
          .where((msg) => msg.status_PUPPII == MessageStatus_PUPPII.sent)
          .map((msg) => {
                'role': msg.isUser_PUPPII ? 'user' : 'assistant',
                'content': msg.content_PUPPII,
              })
          .toList();

      // 调用API获取回复
      final response = await _apiService_PUPPII.sendMessage_PUPPII(
        userMessage: messageText,
        character: widget.character_PUPPII,
        conversationHistory: conversationHistory,
      );

      // 打字机效果显示回复
      await _typewriterEffect_PUPPII(aiMessageId, response);

      // API调用成功后才扣除金币
      await _userService_PUPPII.subtractCoins_PUPPII(costPerMessage);
    } catch (e) {
      print('[ChatPage] Error sending message with coins: $e');
      // 更新消息状态为失败
      await ChatStorageService_PUPPII.updateMessageStatus_PUPPII(
        _currentSession_PUPPII!.sessionId_PUPPII,
        aiMessageId,
        MessageStatus_PUPPII.failed,
        content: 'Sorry, I encountered an error. Please try again.',
      );
    } finally {
      setState(() {
        _isSending_PUPPII = false;
        _typingMessageId_PUPPII = null;
      });
      _refreshSession_PUPPII();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading_PUPPII) {
      return Scaffold(
        backgroundColor: AppTheme_PUPPII.backgroundColor_PUPPII,
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_PUPPII.backgroundColor_PUPPII,
        body: Column(
          children: [
            // 页头
            _buildHeader_PUPPII(),

            // 聊天内容
            Expanded(
              child: _buildChatContent_PUPPII(),
            ),

            // 输入框
            _buildInputArea_PUPPII(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader_PUPPII() {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 10.h,
        bottom: 10.h,
        left: 16.w,
        right: 16.w,
      ),
      decoration: BoxDecoration(
        color: AppTheme_PUPPII.neumorphismBase_PUPPII,
        boxShadow: [
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
            offset: Offset(0, 2.h),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 40.w,
              height: 40.w,
              decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: 18.sp,
                color: AppTheme_PUPPII.textPrimary_PUPPII,
              ),
            ),
          ),

          SizedBox(width: 12.w),

          // 助手信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.character_PUPPII.nickname_PUPPII,
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                GetX<CharacterStateService_PUPPII>(
                  builder: (controller) {
                    final currentCharacter = controller.getCharacterById_PUPPII(
                        widget.character_PUPPII.id_PUPPII);
                    final freeCount =
                        currentCharacter?.freeChatCount_PUPPII ?? 0;

                    return Text(
                      '@${widget.character_PUPPII.nickname_PUPPII} • $freeCount chats left',
                      style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                        fontSize: 12.sp,
                        color: AppTheme_PUPPII.textSecondary_PUPPII,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // 收藏按钮
          GetX<UserService_PUPPII>(
            builder: (userService) {
              final isFavorite = userService.isFavoriteCharacter_PUPPII(
                widget.character_PUPPII.id_PUPPII,
              );

              return GestureDetector(
                onTap: _toggleFavorite_PUPPII,
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    size: 18.sp,
                    color: isFavorite
                        ? Colors.red
                        : AppTheme_PUPPII.textSecondary_PUPPII,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildChatContent_PUPPII() {
    final messageCount = _currentSession_PUPPII?.messages_PUPPII.length ?? 0;
    final isEmpty = messageCount == 0;

    return ListView.builder(
      controller: _scrollController_PUPPII,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      itemCount: isEmpty ? 2 : messageCount + 1, // 信息模块 + 消息/空状态
      itemBuilder: (context, index) {
        // 第一个item始终是助手个人信息
        if (index == 0) {
          return _buildCharacterInfo_PUPPII();
        }

        // 如果没有消息，显示空状态
        if (isEmpty) {
          return _buildEmptyState_PUPPII();
        }

        // 其余是聊天消息
        final messageIndex = index - 1;
        final message = _currentSession_PUPPII!.messages_PUPPII[messageIndex];
        return _buildMessageBubble_PUPPII(message);
      },
    );
  }

  Widget _buildCharacterInfo_PUPPII() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: AppTheme_PUPPII.neumorphismCard_PUPPII,
      child: Column(
        children: [
          // 头像和昵称
          Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismDark_PUPPII
                          .withOpacity(0.3),
                      offset: Offset(3.w, 3.h),
                      blurRadius: 6,
                    ),
                  ],
                ),
                child: ClipOval(
                  child: Image.asset(
                    widget.character_PUPPII.avatarPath_PUPPII,
                    width: 60.w,
                    height: 60.w,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppTheme_PUPPII.primaryColor_PUPPII
                              .withOpacity(0.1),
                        ),
                        child: Icon(
                          Icons.person,
                          size: 30.sp,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      );
                    },
                  ),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.character_PUPPII.nickname_PUPPII,
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      widget.character_PUPPII.shortIntroduction_PUPPII,
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        fontSize: 14.sp,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // 长介绍
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.character_PUPPII.longIntroduction_PUPPII,
                style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                  fontSize: 12.sp,
                  height: 1.4,
                ),
                maxLines: _isExpanded_PUPPII ? null : 3,
                overflow: _isExpanded_PUPPII ? null : TextOverflow.ellipsis,
              ),

              SizedBox(height: 8.h),

              // 展开/收起按钮
              GestureDetector(
                onTap: () =>
                    setState(() => _isExpanded_PUPPII = !_isExpanded_PUPPII),
                child: Text(
                  _isExpanded_PUPPII ? 'Show Less' : 'Show More',
                  style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                    fontSize: 12.sp,
                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState_PUPPII() {
    return Container(
      height: 200.h, // 给空状态一个固定高度
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 60.sp,
              color: AppTheme_PUPPII.textLight_PUPPII,
            ),
            SizedBox(height: 16.h),
            Text(
              'Start a conversation with ${widget.character_PUPPII.nickname_PUPPII}',
              style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                fontSize: 16.sp,
                color: AppTheme_PUPPII.textSecondary_PUPPII,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble_PUPPII(ChatMessage_PUPPII message) {
    final isUser = message.isUser_PUPPII;
    final isTyping = message.status_PUPPII == MessageStatus_PUPPII.typing;
    final isFailed = message.status_PUPPII == MessageStatus_PUPPII.failed;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // AI头像
            Container(
              width: 32.w,
              height: 32.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color:
                        AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.2),
                    offset: Offset(1.w, 1.h),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  widget.character_PUPPII.avatarPath_PUPPII,
                  width: 32.w,
                  height: 32.w,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppTheme_PUPPII.primaryColor_PUPPII
                            .withOpacity(0.1),
                      ),
                      child: Icon(
                        Icons.smart_toy,
                        size: 16.sp,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                      ),
                    );
                  },
                ),
              ),
            ),
            SizedBox(width: 8.w),
          ],

          // 消息气泡
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                Container(
                  constraints: BoxConstraints(maxWidth: 280.w),
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  decoration: BoxDecoration(
                    color: isUser
                        ? AppTheme_PUPPII.primaryColor_PUPPII
                        : AppTheme_PUPPII.neumorphismBase_PUPPII,
                    borderRadius: BorderRadius.circular(18),
                    border: isFailed
                        ? Border.all(
                            color: AppTheme_PUPPII.errorColor_PUPPII, width: 1)
                        : null,
                    boxShadow: [
                      BoxShadow(
                        color: isUser
                            ? AppTheme_PUPPII.primaryColor_PUPPII
                                .withOpacity(0.3)
                            : AppTheme_PUPPII.neumorphismDark_PUPPII
                                .withOpacity(0.2),
                        offset: Offset(2.w, 2.h),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (isTyping && message.content_PUPPII.isEmpty)
                        _buildTypingIndicator_PUPPII()
                      else
                        Text(
                          message.content_PUPPII,
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                            color: isUser
                                ? Colors.white
                                : AppTheme_PUPPII.textPrimary_PUPPII,
                            height: 1.4,
                          ),
                        ),
                      if (isFailed) ...[
                        SizedBox(height: 4.h),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 12.sp,
                              color: AppTheme_PUPPII.errorColor_PUPPII,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              'Failed to send',
                              style: AppTheme_PUPPII.caption_PUPPII.copyWith(
                                fontSize: 10.sp,
                                color: AppTheme_PUPPII.errorColor_PUPPII,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),

                SizedBox(height: 4.h),

                // 时间戳
                Text(
                  _formatTime_PUPPII(message.timestamp_PUPPII),
                  style: AppTheme_PUPPII.caption_PUPPII.copyWith(
                    fontSize: 10.sp,
                    color: AppTheme_PUPPII.textLight_PUPPII,
                  ),
                ),
              ],
            ),
          ),

          if (isUser) ...[
            SizedBox(width: 8.w),
            // 用户头像
            Container(
              width: 32.w,
              height: 32.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color:
                        AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.2),
                    offset: Offset(1.w, 1.h),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: ClipOval(
                child: GetX<UserService_PUPPII>(
                  builder: (userService) {
                    final user = userService.user;
                    final avatarPath = user?.avatarPath_PUPPII ??
                        'assets_puppii/images_puppii/user_avatar.png';

                    return Image.asset(
                      avatarPath,
                      width: 32.w,
                      height: 32.w,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppTheme_PUPPII.secondaryColor_PUPPII
                                .withOpacity(0.1),
                          ),
                          child: Icon(
                            Icons.person,
                            size: 16.sp,
                            color: AppTheme_PUPPII.secondaryColor_PUPPII,
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator_PUPPII() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (int i = 0; i < 3; i++) ...[
          AnimatedContainer(
            duration: Duration(milliseconds: 600 + (i * 200)),
            curve: Curves.easeInOut,
            width: 6.w,
            height: 6.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppTheme_PUPPII.textSecondary_PUPPII.withOpacity(0.6),
            ),
          ),
          if (i < 2) SizedBox(width: 4.w),
        ],
      ],
    );
  }

  String _formatTime_PUPPII(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  Widget _buildInputArea_PUPPII() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppTheme_PUPPII.neumorphismBase_PUPPII,
        boxShadow: [
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
            offset: Offset(0, -2.h),
            blurRadius: 4,
          ),
        ],
      ),
      child: SizedBox(
        child: Row(
          children: [
            // 输入框
            Expanded(
              child: Container(
                decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                  borderRadius: BorderRadius.circular(24),
                ),
                child: TextField(
                  controller: _messageController_PUPPII,
                  style:
                      AppTheme_PUPPII.bodyText_PUPPII.copyWith(fontSize: 14.sp),
                  decoration: InputDecoration(
                    hintText: 'Type a message...',
                    hintStyle: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                      fontSize: 14.sp,
                      color: AppTheme_PUPPII.textLight_PUPPII,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 20.w,
                      vertical: 12.h,
                    ),
                  ),
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _sendMessage_PUPPII(),
                ),
              ),
            ),

            SizedBox(width: 12.w),

            // 发送按钮
            GestureDetector(
              onTap: _isSending_PUPPII ? null : _sendMessage_PUPPII,
              child: Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: _isSending_PUPPII
                      ? AppTheme_PUPPII.textLight_PUPPII
                      : AppTheme_PUPPII.primaryColor_PUPPII,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: (_isSending_PUPPII
                              ? AppTheme_PUPPII.textLight_PUPPII
                              : AppTheme_PUPPII.primaryColor_PUPPII)
                          .withOpacity(0.3),
                      offset: Offset(2.w, 2.h),
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: _isSending_PUPPII
                    ? SizedBox(
                        width: 20.w,
                        height: 20.w,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme_PUPPII.neumorphismBase_PUPPII,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.send,
                        size: 20.sp,
                        color: Colors.white,
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
