import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets_PUPPII_klmn/custom_bottom_nav_PUPPII_cdef.dart';
import '../services_PUPPII_uvwx/soul_question_service_PUPPII_abcd.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import 'home_page_PUPPII_qrst.dart';
import 'history_page_PUPPII_uvwx.dart';
import 'profile_page_PUPPII_yzab.dart';

class MainNavigation_PUPPII extends StatefulWidget {
  const MainNavigation_PUPPII({super.key});

  @override
  State<MainNavigation_PUPPII> createState() => _MainNavigationState_PUPPII();
}

class _MainNavigationState_PUPPII extends State<MainNavigation_PUPPII> {
  int _currentIndex_PUPPII = 0;

  @override
  void initState() {
    super.initState();
    // Initialize UserService first, then SoulQuestionService
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Ensure UserService is initialized
      if (!Get.isRegistered<UserService_PUPPII>()) {
        Get.put(UserService_PUPPII());
      }
    });
  }

  final List<Widget> _pages_PUPPII = [
    const HomePage_PUPPII(),
    const HistoryPage_PUPPII(),
    const ProfilePage_PUPPII(),
  ];

  void _onTabTapped_PUPPII(int index) {
    setState(() {
      _currentIndex_PUPPII = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Main content
          IndexedStack(
            index: _currentIndex_PUPPII,
            children: _pages_PUPPII,
          ),

          // Custom bottom navigation
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: CustomBottomNav_PUPPII(
              currentIndex_PUPPII: _currentIndex_PUPPII,
              onTap_PUPPII: _onTabTapped_PUPPII,
            ),
          ),
        ],
      ),
    );
  }
}
