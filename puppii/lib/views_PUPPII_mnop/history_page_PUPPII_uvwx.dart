import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../services_PUPPII_uvwx/chat_storage_service_PUPPII_cdef.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../models_PUPPII_efgh/chat_session_PUPPII_qrst.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../widgets_PUPPII_klmn/history_item_PUPPII_cdef.dart';
import '../widgets_PUPPII_klmn/custom_background_PUPPII_mnop.dart';

class HistoryPage_PUPPII extends StatefulWidget {
  const HistoryPage_PUPPII({super.key});

  @override
  State<HistoryPage_PUPPII> createState() => _HistoryPageState_PUPPII();
}

class _HistoryPageState_PUPPII extends State<HistoryPage_PUPPII>
    with AutomaticKeepAliveClientMixin {
  final CharacterStateService_PUPPII _characterService_PUPPII =
      Get.find<CharacterStateService_PUPPII>();
  List<ChatSession_PUPPII> _sessions_PUPPII = [];
  bool _isLoading_PUPPII = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadChatSessions_PUPPII();

    // 监听历史更新触发器
    ever(_characterService_PUPPII.historyUpdateTrigger_PUPPII, (_) {
      _loadChatSessions_PUPPII();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次页面变为可见时刷新数据
    _loadChatSessions_PUPPII();
  }

  Future<void> _loadChatSessions_PUPPII() async {
    if (!mounted) return;
    setState(() => _isLoading_PUPPII = true);

    try {
      final sessions =
          await ChatStorageService_PUPPII.getAllChatSessions_PUPPII();

      // 过滤掉没有消息的会话
      final sessionsWithMessages = sessions
          .where((session) => session.messages_PUPPII.isNotEmpty)
          .toList();

      // 按角色分组，每个角色只保留最新的会话
      final Map<String, ChatSession_PUPPII> latestSessions = {};

      for (final session in sessionsWithMessages) {
        final characterId = session.character_PUPPII.id_PUPPII;

        if (!latestSessions.containsKey(characterId) ||
            session.lastUpdated_PUPPII
                .isAfter(latestSessions[characterId]!.lastUpdated_PUPPII)) {
          latestSessions[characterId] = session;
        }
      }

      // 按最后更新时间排序
      final sortedSessions = latestSessions.values.toList()
        ..sort((a, b) => b.lastUpdated_PUPPII.compareTo(a.lastUpdated_PUPPII));

      if (mounted) {
        setState(() {
          _sessions_PUPPII = sortedSessions;
          _isLoading_PUPPII = false;
        });
      }
    } catch (e) {
      print('[HistoryPage] Error loading sessions: $e');
      if (mounted) {
        setState(() => _isLoading_PUPPII = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用super.build for AutomaticKeepAliveClientMixin
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_PUPPII.backgroundColor_PUPPII,
        body: CustomBackground_PUPPII(
          child: CustomScrollView(
            slivers: [
              // 页面标题
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.fromLTRB(20.w, 60.h, 20.w, 20.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Chat History',
                        style: GoogleFonts.comfortaa(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w700,
                          color: AppTheme_PUPPII.textPrimary_PUPPII,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 内容区域
              if (_isLoading_PUPPII)
                SliverToBoxAdapter(
                  child: Container(
                    height: 400.h,
                    child: Center(
                      child: CircularProgressIndicator(
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                      ),
                    ),
                  ),
                )
              else if (_sessions_PUPPII.isEmpty)
                SliverToBoxAdapter(
                  child: Container(
                    height: 400.h,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 280.w,
                            height: 200.h,
                            decoration:
                                AppTheme_PUPPII.neumorphismElevated_PUPPII,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.chat_bubble_outline,
                                  size: 60.sp,
                                  color: AppTheme_PUPPII.textLight_PUPPII,
                                ),
                                SizedBox(height: 16.h),
                                Text(
                                  'No Chat History',
                                  style:
                                      AppTheme_PUPPII.heading3_PUPPII.copyWith(
                                    fontSize: 20.sp,
                                    color: AppTheme_PUPPII.textSecondary_PUPPII,
                                  ),
                                ),
                                SizedBox(height: 8.h),
                                Text(
                                  'Start chatting with AI assistants\nto see your history here',
                                  style: AppTheme_PUPPII.bodyTextSmall_PUPPII
                                      .copyWith(
                                    fontSize: 12.sp,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else
                // 历史记录列表
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final session = _sessions_PUPPII[index];
                      final isLeftAligned = index % 2 == 0;

                      return HistoryItem_PUPPII(
                        character_PUPPII: session.character_PUPPII,
                        session_PUPPII: session,
                        isLeftAligned_PUPPII: isLeftAligned,
                        onDelete_PUPPII: () {
                          _loadChatSessions_PUPPII(); // 重新加载数据
                        },
                      );
                    },
                    childCount: _sessions_PUPPII.length,
                  ),
                ),

              // 底部空间，为导航栏留空
              SliverToBoxAdapter(
                child: SizedBox(height: 120.h),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
