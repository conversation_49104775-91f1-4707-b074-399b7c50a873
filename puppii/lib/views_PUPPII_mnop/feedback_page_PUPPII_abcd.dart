import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';

class FeedbackPage_PUPPII_abcd extends StatefulWidget {
  const FeedbackPage_PUPPII_abcd({super.key});

  @override
  State<FeedbackPage_PUPPII_abcd> createState() =>
      _FeedbackPageState_PUPPII_abcd();
}

class _FeedbackPageState_PUPPII_abcd extends State<FeedbackPage_PUPPII_abcd> {
  final TextEditingController _messageController_PUPPII =
      TextEditingController();
  final ImagePicker _imageSelector_PUPPII = ImagePicker();
  final AudioRecorder _voiceRecorder_PUPPII = AudioRecorder();
  final AudioPlayer _soundPlayer_PUPPII = AudioPlayer();

  List<File> _attachedImages_PUPPII = [];
  String? _recordedAudioPath_PUPPII;
  bool _isRecordingActive_PUPPII = false;
  bool _isAudioPlaying_PUPPII = false;
  Duration _recordingTime_PUPPII = Duration.zero;
  Timer? _recordingTimer_PUPPII;

  @override
  void dispose() {
    _messageController_PUPPII.dispose();
    _voiceRecorder_PUPPII.dispose();
    _soundPlayer_PUPPII.dispose();
    _recordingTimer_PUPPII?.cancel();
    super.dispose();
  }

  bool get _hasValidContent_PUPPII =>
      _messageController_PUPPII.text.trim().isNotEmpty ||
      _recordedAudioPath_PUPPII != null ||
      _attachedImages_PUPPII.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_PUPPII.backgroundColor_PUPPII,
        body: Stack(
          children: [
            // 背景装饰圆圈
            Positioned(
              top: -50.h,
              right: -30.w,
              child: Container(
                width: 120.w,
                height: 120.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismDark_PUPPII
                          .withOpacity(0.2),
                      offset: Offset(8.w, 8.h),
                      blurRadius: 20,
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 100.h,
              left: -40.w,
              child: Container(
                width: 80.w,
                height: 80.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.05),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismLight_PUPPII
                          .withOpacity(0.5),
                      offset: Offset(-4.w, -4.h),
                      blurRadius: 15,
                    ),
                  ],
                ),
              ),
            ),

            // 主要内容
            SafeArea(
              child: CustomScrollView(
                slivers: [
                  // 自定义头部
                  SliverToBoxAdapter(
                    child: _buildCustomHeader_PUPPII(),
                  ),

                  // 内容区域
                  SliverPadding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    sliver: SliverList(
                      delegate: SliverChildListDelegate([
                        SizedBox(height: 30.h),

                        // 卡片式布局 - 文字反馈
                        _buildCardSection_PUPPII(
                          title: 'Share Your Thoughts',
                          icon: Icons.edit_note_rounded,
                          child: _buildTextFeedbackContent_PUPPII(),
                        ),

                        SizedBox(height: 24.h),

                        // 卡片式布局 - 语音反馈
                        _buildCardSection_PUPPII(
                          title: 'Voice Message',
                          icon: Icons.mic_rounded,
                          child: _buildVoiceContent_PUPPII(),
                        ),

                        SizedBox(height: 24.h),

                        // 卡片式布局 - 图片上传
                        _buildCardSection_PUPPII(
                          title: 'Add Images',
                          icon: Icons.photo_library_rounded,
                          child: _buildImageContent_PUPPII(),
                        ),

                        SizedBox(height: 100.h), // 为浮动按钮留出空间
                      ]),
                    ),
                  ),
                ],
              ),
            ),

            // 浮动提交按钮
            _buildFloatingSubmitButton_PUPPII(),
          ],
        ),
      ),
    );
  }

  // 全新的自定义头部设计
  Widget _buildCustomHeader_PUPPII() {
    return Container(
      margin: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 0),
      child: Column(
        children: [
          // 返回按钮和标题行
          Row(
            children: [
              // 圆形返回按钮
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: 50.w,
                  height: 50.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppTheme_PUPPII.backgroundColor_PUPPII,
                    boxShadow: [
                      // 深色外阴影
                      BoxShadow(
                        color: AppTheme_PUPPII.neumorphismDark_PUPPII
                            .withOpacity(0.4),
                        offset: Offset(6.w, 6.h),
                        blurRadius: 12,
                        spreadRadius: 0,
                      ),
                      // 浅色内阴影
                      BoxShadow(
                        color: AppTheme_PUPPII.neumorphismLight_PUPPII
                            .withOpacity(0.9),
                        offset: Offset(-4.w, -4.h),
                        blurRadius: 8,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.arrow_back_rounded,
                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                    size: 24.sp,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 30.h),

          // 大标题区域
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 20.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25.r),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme_PUPPII.backgroundColor_PUPPII,
                  AppTheme_PUPPII.backgroundColor_PUPPII.withOpacity(0.8),
                ],
              ),
              boxShadow: [
                // 主要阴影
                BoxShadow(
                  color:
                      AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                  offset: Offset(8.w, 8.h),
                  blurRadius: 16,
                  spreadRadius: 0,
                ),
                // 高光
                BoxShadow(
                  color:
                      AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.8),
                  offset: Offset(-6.w, -6.h),
                  blurRadius: 12,
                  spreadRadius: 0,
                ),
                // 内部高光
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  offset: Offset(2.w, 2.h),
                  blurRadius: 4,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Column(
              children: [
                Text(
                  'Share Your Experience',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.comfortaa(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w800,
                    color: AppTheme_PUPPII.textPrimary_PUPPII,
                    height: 1.2,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Your feedback helps us create better experiences',
                  textAlign: TextAlign.center,
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 14.sp,
                    color: AppTheme_PUPPII.textSecondary_PUPPII,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 卡片式布局容器
  Widget _buildCardSection_PUPPII({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        color: AppTheme_PUPPII.backgroundColor_PUPPII,
        boxShadow: [
          // 主要深色阴影
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.25),
            offset: Offset(10.w, 10.h),
            blurRadius: 20,
            spreadRadius: 0,
          ),
          // 浅色高光
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.9),
            offset: Offset(-8.w, -8.h),
            blurRadius: 16,
            spreadRadius: 0,
          ),
          // 边缘高光
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            offset: Offset(1.w, 1.h),
            blurRadius: 2,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Container(
            width: double.infinity,
            padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 16.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.08),
                  AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.03),
                ],
              ),
            ),
            child: Row(
              children: [
                // 图标容器
                Container(
                  width: 36.w,
                  height: 36.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.15),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme_PUPPII.primaryColor_PUPPII
                            .withOpacity(0.2),
                        offset: Offset(2.w, 2.h),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                    size: 18.sp,
                  ),
                ),

                SizedBox(width: 12.w),

                // 标题文字
                Text(
                  title,
                  style: GoogleFonts.comfortaa(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w700,
                    color: AppTheme_PUPPII.textPrimary_PUPPII,
                  ),
                ),
              ],
            ),
          ),

          // 内容区域
          Padding(
            padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 20.h),
            child: child,
          ),
        ],
      ),
    );
  }

  // 文字反馈内容
  Widget _buildTextFeedbackContent_PUPPII() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: AppTheme_PUPPII.backgroundColor_PUPPII,
        boxShadow: [
          // 内凹效果
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.2),
            offset: Offset(4.w, 4.h),
            blurRadius: 8,
            spreadRadius: -2,
          ),
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.7),
            offset: Offset(-3.w, -3.h),
            blurRadius: 6,
            spreadRadius: -1,
          ),
        ],
      ),
      child: TextField(
        controller: _messageController_PUPPII,
        maxLines: 6,
        style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
          fontSize: 16.sp,
          height: 1.5,
        ),
        decoration: InputDecoration(
          hintText: 'Share your thoughts, suggestions, or report issues...',
          hintStyle: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
            color: AppTheme_PUPPII.textSecondary_PUPPII.withOpacity(0.6),
            fontSize: 15.sp,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16.w),
        ),
      ),
    );
  }

  // 语音反馈内容
  Widget _buildVoiceContent_PUPPII() {
    return Column(
      children: [
        // 录音按钮区域
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 20.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: AppTheme_PUPPII.backgroundColor_PUPPII,
            boxShadow: [
              // 内凹效果
              BoxShadow(
                color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.15),
                offset: Offset(3.w, 3.h),
                blurRadius: 6,
                spreadRadius: -1,
              ),
              BoxShadow(
                color: AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.8),
                offset: Offset(-2.w, -2.h),
                blurRadius: 4,
                spreadRadius: -1,
              ),
            ],
          ),
          child: Column(
            children: [
              // 大录音按钮
              GestureDetector(
                onTap: _isRecordingActive_PUPPII
                    ? _stopRecording_PUPPII
                    : _startRecording_PUPPII,
                child: Container(
                  width: 80.w,
                  height: 80.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: _isRecordingActive_PUPPII
                        ? LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.red.withOpacity(0.8),
                              Colors.red.withOpacity(0.6),
                            ],
                          )
                        : LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppTheme_PUPPII.primaryColor_PUPPII
                                  .withOpacity(0.8),
                              AppTheme_PUPPII.primaryColor_PUPPII
                                  .withOpacity(0.6),
                            ],
                          ),
                    boxShadow: [
                      BoxShadow(
                        color: (_isRecordingActive_PUPPII
                                ? Colors.red
                                : AppTheme_PUPPII.primaryColor_PUPPII)
                            .withOpacity(0.3),
                        offset: Offset(4.w, 4.h),
                        blurRadius: 12,
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: Colors.white.withOpacity(0.2),
                        offset: Offset(-2.w, -2.h),
                        blurRadius: 6,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    _isRecordingActive_PUPPII
                        ? Icons.stop_rounded
                        : Icons.mic_rounded,
                    color: Colors.white,
                    size: 36.sp,
                  ),
                ),
              ),

              SizedBox(height: 16.h),

              // 状态文字
              Text(
                _isRecordingActive_PUPPII ? 'Recording...' : 'Tap to record',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: _isRecordingActive_PUPPII
                      ? Colors.red
                      : AppTheme_PUPPII.primaryColor_PUPPII,
                ),
              ),

              if (_isRecordingActive_PUPPII) ...[
                SizedBox(height: 8.h),
                // 录音时长显示
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: Colors.red.withOpacity(0.1),
                    border: Border.all(
                      color: Colors.red.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '${_recordingTime_PUPPII.inMinutes.toString().padLeft(2, '0')}:${(_recordingTime_PUPPII.inSeconds % 60).toString().padLeft(2, '0')}',
                    style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),

        // 录音播放区域
        if (_recordedAudioPath_PUPPII != null) ...[
          SizedBox(height: 16.h),
          _buildAudioPlaybackWidget_PUPPII(),
        ],
      ],
    );
  }

  // 音频播放组件
  Widget _buildAudioPlaybackWidget_PUPPII() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: AppTheme_PUPPII.backgroundColor_PUPPII,
        border: Border.all(
          color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
            offset: Offset(0, 2.h),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // 播放按钮
          GestureDetector(
            onTap: _toggleAudioPlayback_PUPPII,
            child: Container(
              width: 44.w,
              height: 44.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppTheme_PUPPII.primaryColor_PUPPII,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.3),
                    offset: Offset(2.w, 2.h),
                    blurRadius: 6,
                  ),
                ],
              ),
              child: Icon(
                _isAudioPlaying_PUPPII
                    ? Icons.pause_rounded
                    : Icons.play_arrow_rounded,
                color: Colors.white,
                size: 24.sp,
              ),
            ),
          ),

          SizedBox(width: 12.w),

          // 音频信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Voice Message',
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme_PUPPII.textPrimary_PUPPII,
                  ),
                ),
                Text(
                  _isAudioPlaying_PUPPII ? 'Playing...' : 'Tap to play',
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 12.sp,
                    color: AppTheme_PUPPII.textSecondary_PUPPII,
                  ),
                ),
              ],
            ),
          ),

          // 删除按钮
          GestureDetector(
            onTap: () {
              setState(() {
                _recordedAudioPath_PUPPII = null;
              });
            },
            child: Container(
              width: 32.w,
              height: 32.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withOpacity(0.1),
                border: Border.all(
                  color: Colors.red.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                Icons.delete_outline_rounded,
                color: Colors.red,
                size: 16.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 图片上传内容
  Widget _buildImageContent_PUPPII() {
    return Column(
      children: [
        // 添加图片按钮
        GestureDetector(
          onTap: _showImageSourceDialog_PUPPII,
          child: Container(
            width: double.infinity,
            height: 120.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.r),
              color: AppTheme_PUPPII.backgroundColor_PUPPII,
              border: Border.all(
                color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.3),
                width: 2,
                style: BorderStyle.solid,
              ),
              boxShadow: [
                // 内凹效果
                BoxShadow(
                  color:
                      AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.1),
                  offset: Offset(2.w, 2.h),
                  blurRadius: 4,
                  spreadRadius: -1,
                ),
                BoxShadow(
                  color:
                      AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.9),
                  offset: Offset(-1.w, -1.h),
                  blurRadius: 2,
                  spreadRadius: -1,
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 50.w,
                  height: 50.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.15),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme_PUPPII.primaryColor_PUPPII
                            .withOpacity(0.2),
                        offset: Offset(2.w, 2.h),
                        blurRadius: 6,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.add_photo_alternate_rounded,
                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                    size: 24.sp,
                  ),
                ),
                SizedBox(height: 12.h),
                Text(
                  'Add Images',
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                  ),
                ),
                Text(
                  'Tap to select from camera or gallery',
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 12.sp,
                    color: AppTheme_PUPPII.textSecondary_PUPPII,
                  ),
                ),
              ],
            ),
          ),
        ),

        // 已选择的图片网格
        if (_attachedImages_PUPPII.isNotEmpty) ...[
          SizedBox(height: 16.h),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
              childAspectRatio: 1,
            ),
            itemCount: _attachedImages_PUPPII.length,
            itemBuilder: (context, index) {
              return _buildImagePreview_PUPPII(index);
            },
          ),
        ],
      ],
    );
  }

  // 图片预览组件
  Widget _buildImagePreview_PUPPII(int index) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.2),
            offset: Offset(3.w, 3.h),
            blurRadius: 6,
          ),
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismLight_PUPPII.withOpacity(0.8),
            offset: Offset(-2.w, -2.h),
            blurRadius: 4,
          ),
        ],
      ),
      child: Stack(
        children: [
          // 图片
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Image.file(
              _attachedImages_PUPPII[index],
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),

          // 删除按钮
          Positioned(
            top: 4.h,
            right: 4.w,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _attachedImages_PUPPII.removeAt(index);
                });
              },
              child: Container(
                width: 24.w,
                height: 24.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.red.withOpacity(0.9),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      offset: Offset(1.w, 1.h),
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.close_rounded,
                  color: Colors.white,
                  size: 14.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 浮动提交按钮
  Widget _buildFloatingSubmitButton_PUPPII() {
    return Positioned(
      bottom: 30.h,
      left: 24.w,
      right: 24.w,
      child: GestureDetector(
        onTap: _submitFeedback_PUPPII,
        child: Container(
          width: double.infinity,
          height: 56.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(28.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme_PUPPII.primaryColor_PUPPII,
                AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.8),
              ],
            ),
            boxShadow: [
              // 主要阴影
              BoxShadow(
                color: AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.4),
                offset: Offset(0, 8.h),
                blurRadius: 20,
                spreadRadius: 0,
              ),
              // 深色阴影
              BoxShadow(
                color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                offset: Offset(4.w, 4.h),
                blurRadius: 12,
                spreadRadius: 0,
              ),
              // 高光
              BoxShadow(
                color: Colors.white.withOpacity(0.2),
                offset: Offset(-2.w, -2.h),
                blurRadius: 6,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.send_rounded,
                color: Colors.white,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Submit Feedback',
                style: GoogleFonts.comfortaa(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 录音相关方法
  Future<void> _startRecording_PUPPII() async {
    if (!await _voiceRecorder_PUPPII.hasPermission()) {
      final status = await Permission.microphone.request();
      if (!status.isGranted) {
        _showErrorMessage_PUPPII(
            'Microphone permission is required for voice recording');
        return;
      }
    }

    try {
      final directory = await getTemporaryDirectory();
      final path =
          '${directory.path}/feedback_voice_${DateTime.now().millisecondsSinceEpoch}.m4a';

      await _voiceRecorder_PUPPII.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: path,
      );

      setState(() {
        _isRecordingActive_PUPPII = true;
        _recordingTime_PUPPII = Duration.zero;
      });

      _recordingTimer_PUPPII =
          Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingTime_PUPPII += const Duration(seconds: 1);
        });
      });
    } catch (e) {
      _showErrorMessage_PUPPII('Failed to start recording: $e');
    }
  }

  Future<void> _stopRecording_PUPPII() async {
    try {
      final path = await _voiceRecorder_PUPPII.stop();
      _recordingTimer_PUPPII?.cancel();

      setState(() {
        _isRecordingActive_PUPPII = false;
        _recordedAudioPath_PUPPII = path;
      });
    } catch (e) {
      _showErrorMessage_PUPPII('Failed to stop recording: $e');
    }
  }

  Future<void> _toggleAudioPlayback_PUPPII() async {
    if (_recordedAudioPath_PUPPII == null) return;

    if (_isAudioPlaying_PUPPII) {
      await _soundPlayer_PUPPII.pause();
      setState(() {
        _isAudioPlaying_PUPPII = false;
      });
    } else {
      await _soundPlayer_PUPPII
          .play(DeviceFileSource(_recordedAudioPath_PUPPII!));
      setState(() {
        _isAudioPlaying_PUPPII = true;
      });

      _soundPlayer_PUPPII.onPlayerComplete.listen((event) {
        setState(() {
          _isAudioPlaying_PUPPII = false;
        });
      });
    }
  }

  void _deleteRecording_PUPPII() {
    setState(() {
      _recordedAudioPath_PUPPII = null;
      _isAudioPlaying_PUPPII = false;
    });
  }

  // 图片相关方法
  Future<void> _selectImage_PUPPII(ImageSource source) async {
    if (_attachedImages_PUPPII.length >= 6) {
      _showErrorMessage_PUPPII('Maximum 6 images allowed');
      return;
    }

    try {
      Permission permission =
          source == ImageSource.camera ? Permission.camera : Permission.photos;
      PermissionStatus status = await permission.request();

      if (status.isDenied) {
        String message = source == ImageSource.camera
            ? 'Camera permission is required to take photos'
            : 'Photo library permission is required to select images';
        _showErrorMessage_PUPPII(message);
        return;
      }

      final XFile? image = await _imageSelector_PUPPII.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _attachedImages_PUPPII.add(File(image.path));
        });
      }
    } catch (e) {
      _showErrorMessage_PUPPII('Failed to pick image: $e');
    }
  }

  void _showImageSourceDialog_PUPPII() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Image Source',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        _selectImage_PUPPII(ImageSource.camera);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.camera_alt,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                              size: 24.sp,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Camera',
                              style:
                                  AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                                fontSize: 14.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        _selectImage_PUPPII(ImageSource.gallery);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.photo_library,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                              size: 24.sp,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Gallery',
                              style:
                                  AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                                fontSize: 14.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _removeImage_PUPPII(int index) {
    setState(() {
      _attachedImages_PUPPII.removeAt(index);
    });
  }

  // 提交和工具方法
  Future<void> _submitFeedback_PUPPII() async {
    if (!_hasValidContent_PUPPII) return;

    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme_PUPPII.primaryColor_PUPPII),
              ),
              SizedBox(height: 16.h),
              Text(
                'Submitting feedback...',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 16.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );

    try {
      // 模拟提交过程
      await Future.delayed(const Duration(seconds: 1));

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 返回上一页
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Feedback submitted successfully!',
              style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 80.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示错误消息
      if (mounted) {
        _showErrorMessage_PUPPII('Failed to submit feedback: $e');
      }
    }
  }

  void _showErrorMessage_PUPPII(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
            fontSize: 14.sp,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 100.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }
}
