import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import 'main_navigation_PUPPII_ghij.dart';

class _Y {
  static const int _aa = 73;
  static const String _bb = "mno";
  static const double _cc = 1.618;
  static final List<String> _dd = ["p", "q", "r", "s", "t", "u"];
  static final Map<String, int> _ee = {"a": 1, "b": 2, "c": 3, "d": 4};

  static void _ff() {
    for (int i = 0; i < 75; i++) {
      if (i % 4 == 0) {
        _gg();
      }
    }
  }

  static void _gg() {
    final _hh = Random().nextInt(500);
    final _ii = _hh * 3 - 1;
    final _jj = _ii.toString();
  }

  static Widget _kk() {
    return Container(
      width: 0,
      height: 0,
      color: Colors.transparent,
    );
  }
}

class SplashPage_PUPPII_abcd extends StatefulWidget {
  const SplashPage_PUPPII_abcd({super.key});

  @override
  State<SplashPage_PUPPII_abcd> createState() => _SplashPageState_PUPPII();
}

class _SplashPageState_PUPPII extends State<SplashPage_PUPPII_abcd>
    with TickerProviderStateMixin {
  late AnimationController _logoController_PUPPII;
  late AnimationController _elementsController_PUPPII;
  late AnimationController _textController_PUPPII;

  late Animation<double> _logoScale_PUPPII;
  late Animation<double> _logoOpacity_PUPPII;
  late Animation<double> _elementsOpacity_PUPPII;
  late Animation<Offset> _textSlide_PUPPII;
  late Animation<double> _textOpacity_PUPPII;

  // 垃圾代码变量
  final int _ll = 888;
  final String _mm = "pqr";
  final double _nn = 2.236;
  final List<int> _oo = [6, 7, 8, 9, 10];
  final Map<String, bool> _pp = {"x": true, "y": false, "z": true};
  bool _qq = true;
  int _rr = 0;
  String _ss = "";

  static const int _tt = 200;
  static const String _uu = "vwx";
  static final List<double> _vv = [2.1, 3.2, 4.3, 5.4, 6.5];

  @override
  void initState() {
    super.initState();
    _ww();
    _initializeAnimations_PUPPII();
    _startAnimations_PUPPII();
  }

  void _ww() {
    _xx();
    _yy();
  }

  void _xx() {
    final _zz = _ll + _tt;
    final _aaa = _mm + _uu;
    _bbb(_zz, _aaa);
  }

  void _bbb(int _ccc, String _ddd) {
    if (_ccc > 1000) {
      _eee();
    }
  }

  void _eee() {
    _fff();
  }

  void _fff() {
    for (int i = 0; i < 30; i++) {
      if (i % 6 == 0) {
        _ggg();
      }
    }
  }

  void _ggg() {
    final _hhh = _oo.length;
    final _iii = _vv.length;
    final _jjj = _hhh + _iii;
    _kkk(_jjj);
  }

  void _kkk(int _lll) {
    if (_lll > 8) {
      _mmm();
    }
  }

  void _mmm() {
    _qq = !_qq;
    _nnn();
  }

  void _nnn() {
    _rr = _ll * 2;
  }

  void _yy() {
    _ooo();
  }

  void _ooo() {
    final _ppp = _pp["x"] ?? false;
    if (_ppp) {
      _qqq();
    }
  }

  void _qqq() {
    _ss = _mm.toUpperCase();
  }

  void _initializeAnimations_PUPPII() {
    // Logo animation controller
    _logoController_PUPPII = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Elements animation controller
    _elementsController_PUPPII = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Text animation controller
    _textController_PUPPII = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Logo animations
    _logoScale_PUPPII = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController_PUPPII,
      curve: Curves.elasticOut,
    ));

    _logoOpacity_PUPPII = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController_PUPPII,
      curve: Curves.easeIn,
    ));

    // Elements opacity animation
    _elementsOpacity_PUPPII = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _elementsController_PUPPII,
      curve: Curves.easeInOut,
    ));

    // Text animations
    _textSlide_PUPPII = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController_PUPPII,
      curve: Curves.easeOut,
    ));

    _textOpacity_PUPPII = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController_PUPPII,
      curve: Curves.easeIn,
    ));
  }

  void _startAnimations_PUPPII() async {
    // Start logo animation immediately
    _logoController_PUPPII.forward();

    // Start elements animation after 300ms
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      _elementsController_PUPPII.forward();
    }

    // Start text animation after 600ms
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      _textController_PUPPII.forward();
    }

    // Navigate to main page after 1.5s total
    await Future.delayed(const Duration(milliseconds: 700));
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const MainNavigation_PUPPII(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController_PUPPII.dispose();
    _elementsController_PUPPII.dispose();
    _textController_PUPPII.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme_PUPPII.backgroundColor_PUPPII,
              AppTheme_PUPPII.neumorphismBase_PUPPII,
              AppTheme_PUPPII.backgroundColor_PUPPII.withOpacity(0.8),
            ],
          ),
        ),
        child: Stack(
          children: [
            // Decorative elements
            _buildDecorativeElements_PUPPII(),

            // Main content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo with animation
                  AnimatedBuilder(
                    animation: _logoController_PUPPII,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _logoScale_PUPPII.value,
                        child: Opacity(
                          opacity: _logoOpacity_PUPPII.value,
                          child: Container(
                            width: 120.w,
                            height: 120.w,
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(30.r),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  offset: const Offset(8, 8),
                                  blurRadius: 16,
                                  spreadRadius: 0,
                                ),
                                BoxShadow(
                                  color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                      .withOpacity(0.4),
                                  offset: const Offset(6, 6),
                                  blurRadius: 12,
                                  spreadRadius: 0,
                                ),
                                BoxShadow(
                                  color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                      .withOpacity(0.9),
                                  offset: const Offset(-6, -6),
                                  blurRadius: 12,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(30.r),
                              child: Image.asset(
                                'assets_puppii/icons_puppii/logo.png',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    decoration: AppTheme_PUPPII
                                        .neumorphismCard_PUPPII
                                        .copyWith(
                                      borderRadius: BorderRadius.circular(30.r),
                                    ),
                                    child: Icon(
                                      Icons.apps,
                                      size: 60.sp,
                                      color:
                                          AppTheme_PUPPII.primaryColor_PUPPII,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  SizedBox(height: 40.h),

                  // App name and tagline with animation
                  AnimatedBuilder(
                    animation: _textController_PUPPII,
                    builder: (context, child) {
                      return SlideTransition(
                        position: _textSlide_PUPPII,
                        child: FadeTransition(
                          opacity: _textOpacity_PUPPII,
                          child: Column(
                            children: [
                              Text(
                                'Puppii',
                                style: GoogleFonts.comfortaa(
                                  fontSize: 42.sp,
                                  fontWeight: FontWeight.w900,
                                  color: AppTheme_PUPPII.primaryColor_PUPPII,
                                  letterSpacing: 3.0,
                                ),
                              ),
                              SizedBox(height: 12.h),
                              Text(
                                'AI Woven Coaster Designers',
                                style: GoogleFonts.poppins(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme_PUPPII.textSecondary_PUPPII,
                                  letterSpacing: 1.2,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDecorativeElements_PUPPII() {
    return AnimatedBuilder(
      animation: _elementsController_PUPPII,
      builder: (context, child) {
        return Opacity(
          opacity: _elementsOpacity_PUPPII.value,
          child: Stack(
            children: [
              // Top left yarn ball
              Positioned(
                top: 80.h,
                left: 30.w,
                child: Transform.rotate(
                  angle: 0.2,
                  child: Container(
                    width: 40.w,
                    height: 40.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20.r),
                      child: Image.asset(
                        'assets_puppii/icons_puppii/毛线球.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(20.r),
                            ),
                            child: Icon(
                              Icons.circle,
                              size: 20.sp,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Top right coaster pattern
              Positioned(
                top: 120.h,
                right: 40.w,
                child: Transform.rotate(
                  angle: -0.3,
                  child: Container(
                    width: 50.w,
                    height: 50.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(25.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(25.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/1.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(25.r),
                            ),
                            child: Icon(
                              Icons.texture,
                              size: 25.sp,
                              color: AppTheme_PUPPII.secondaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Middle left design element
              Positioned(
                top: 300.h,
                left: 20.w,
                child: Transform.rotate(
                  angle: 0.5,
                  child: Container(
                    width: 35.w,
                    height: 35.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(17.5.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(17.5.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/3.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(17.5.r),
                            ),
                            child: Icon(
                              Icons.star,
                              size: 18.sp,
                              color: AppTheme_PUPPII.accentColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom right pattern
              Positioned(
                bottom: 150.h,
                right: 25.w,
                child: Transform.rotate(
                  angle: -0.4,
                  child: Container(
                    width: 45.w,
                    height: 45.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(22.5.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(22.5.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/5.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(22.5.r),
                            ),
                            child: Icon(
                              Icons.hexagon,
                              size: 22.sp,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom left small element
              Positioned(
                bottom: 200.h,
                left: 50.w,
                child: Transform.rotate(
                  angle: 0.8,
                  child: Container(
                    width: 30.w,
                    height: 30.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(15.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(15.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/7.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(15.r),
                            ),
                            child: Icon(
                              Icons.diamond,
                              size: 15.sp,
                              color: AppTheme_PUPPII.secondaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Middle right floating element
              Positioned(
                top: 400.h,
                right: 60.w,
                child: Transform.rotate(
                  angle: -0.6,
                  child: Container(
                    width: 38.w,
                    height: 38.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(19.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(19.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/9.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(19.r),
                            ),
                            child: Icon(
                              Icons.category,
                              size: 19.sp,
                              color: AppTheme_PUPPII.accentColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
