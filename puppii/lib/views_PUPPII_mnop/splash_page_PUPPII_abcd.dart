import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import 'main_navigation_PUPPII_ghij.dart';

class SplashPage_PUPPII_abcd extends StatefulWidget {
  const SplashPage_PUPPII_abcd({super.key});

  @override
  State<SplashPage_PUPPII_abcd> createState() => _SplashPageState_PUPPII();
}

class _SplashPageState_PUPPII extends State<SplashPage_PUPPII_abcd>
    with TickerProviderStateMixin {
  late AnimationController _logoController_PUPPII;
  late AnimationController _elementsController_PUPPII;
  late AnimationController _textController_PUPPII;

  late Animation<double> _logoScale_PUPPII;
  late Animation<double> _logoOpacity_PUPPII;
  late Animation<double> _elementsOpacity_PUPPII;
  late Animation<Offset> _textSlide_PUPPII;
  late Animation<double> _textOpacity_PUPPII;

  @override
  void initState() {
    super.initState();
    _initializeAnimations_PUPPII();
    _startAnimations_PUPPII();
  }

  void _initializeAnimations_PUPPII() {
    // Logo animation controller
    _logoController_PUPPII = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Elements animation controller
    _elementsController_PUPPII = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Text animation controller
    _textController_PUPPII = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Logo animations
    _logoScale_PUPPII = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController_PUPPII,
      curve: Curves.elasticOut,
    ));

    _logoOpacity_PUPPII = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController_PUPPII,
      curve: Curves.easeIn,
    ));

    // Elements opacity animation
    _elementsOpacity_PUPPII = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _elementsController_PUPPII,
      curve: Curves.easeInOut,
    ));

    // Text animations
    _textSlide_PUPPII = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController_PUPPII,
      curve: Curves.easeOut,
    ));

    _textOpacity_PUPPII = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController_PUPPII,
      curve: Curves.easeIn,
    ));
  }

  void _startAnimations_PUPPII() async {
    // Start logo animation immediately
    _logoController_PUPPII.forward();

    // Start elements animation after 300ms
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      _elementsController_PUPPII.forward();
    }

    // Start text animation after 600ms
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      _textController_PUPPII.forward();
    }

    // Navigate to main page after 1.5s total
    await Future.delayed(const Duration(milliseconds: 700));
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const MainNavigation_PUPPII(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController_PUPPII.dispose();
    _elementsController_PUPPII.dispose();
    _textController_PUPPII.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme_PUPPII.backgroundColor_PUPPII,
              AppTheme_PUPPII.neumorphismBase_PUPPII,
              AppTheme_PUPPII.backgroundColor_PUPPII.withOpacity(0.8),
            ],
          ),
        ),
        child: Stack(
          children: [
            // Decorative elements
            _buildDecorativeElements_PUPPII(),

            // Main content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo with animation
                  AnimatedBuilder(
                    animation: _logoController_PUPPII,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _logoScale_PUPPII.value,
                        child: Opacity(
                          opacity: _logoOpacity_PUPPII.value,
                          child: Container(
                            width: 120.w,
                            height: 120.w,
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(30.r),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  offset: const Offset(8, 8),
                                  blurRadius: 16,
                                  spreadRadius: 0,
                                ),
                                BoxShadow(
                                  color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                      .withOpacity(0.4),
                                  offset: const Offset(6, 6),
                                  blurRadius: 12,
                                  spreadRadius: 0,
                                ),
                                BoxShadow(
                                  color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                      .withOpacity(0.9),
                                  offset: const Offset(-6, -6),
                                  blurRadius: 12,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(30.r),
                              child: Image.asset(
                                'assets_puppii/icons_puppii/logo.png',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    decoration: AppTheme_PUPPII
                                        .neumorphismCard_PUPPII
                                        .copyWith(
                                      borderRadius: BorderRadius.circular(30.r),
                                    ),
                                    child: Icon(
                                      Icons.apps,
                                      size: 60.sp,
                                      color:
                                          AppTheme_PUPPII.primaryColor_PUPPII,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  SizedBox(height: 40.h),

                  // App name and tagline with animation
                  AnimatedBuilder(
                    animation: _textController_PUPPII,
                    builder: (context, child) {
                      return SlideTransition(
                        position: _textSlide_PUPPII,
                        child: FadeTransition(
                          opacity: _textOpacity_PUPPII,
                          child: Column(
                            children: [
                              Text(
                                'Puppii',
                                style: GoogleFonts.comfortaa(
                                  fontSize: 42.sp,
                                  fontWeight: FontWeight.w900,
                                  color: AppTheme_PUPPII.primaryColor_PUPPII,
                                  letterSpacing: 3.0,
                                ),
                              ),
                              SizedBox(height: 12.h),
                              Text(
                                'AI Woven Coaster Designers',
                                style: GoogleFonts.poppins(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme_PUPPII.textSecondary_PUPPII,
                                  letterSpacing: 1.2,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDecorativeElements_PUPPII() {
    return AnimatedBuilder(
      animation: _elementsController_PUPPII,
      builder: (context, child) {
        return Opacity(
          opacity: _elementsOpacity_PUPPII.value,
          child: Stack(
            children: [
              // Top left yarn ball
              Positioned(
                top: 80.h,
                left: 30.w,
                child: Transform.rotate(
                  angle: 0.2,
                  child: Container(
                    width: 40.w,
                    height: 40.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20.r),
                      child: Image.asset(
                        'assets_puppii/icons_puppii/毛线球.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(20.r),
                            ),
                            child: Icon(
                              Icons.circle,
                              size: 20.sp,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Top right coaster pattern
              Positioned(
                top: 120.h,
                right: 40.w,
                child: Transform.rotate(
                  angle: -0.3,
                  child: Container(
                    width: 50.w,
                    height: 50.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(25.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(25.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/1.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(25.r),
                            ),
                            child: Icon(
                              Icons.texture,
                              size: 25.sp,
                              color: AppTheme_PUPPII.secondaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Middle left design element
              Positioned(
                top: 300.h,
                left: 20.w,
                child: Transform.rotate(
                  angle: 0.5,
                  child: Container(
                    width: 35.w,
                    height: 35.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(17.5.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(17.5.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/3.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(17.5.r),
                            ),
                            child: Icon(
                              Icons.star,
                              size: 18.sp,
                              color: AppTheme_PUPPII.accentColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom right pattern
              Positioned(
                bottom: 150.h,
                right: 25.w,
                child: Transform.rotate(
                  angle: -0.4,
                  child: Container(
                    width: 45.w,
                    height: 45.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(22.5.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(22.5.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/5.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(22.5.r),
                            ),
                            child: Icon(
                              Icons.hexagon,
                              size: 22.sp,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom left small element
              Positioned(
                bottom: 200.h,
                left: 50.w,
                child: Transform.rotate(
                  angle: 0.8,
                  child: Container(
                    width: 30.w,
                    height: 30.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(15.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(15.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/7.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(15.r),
                            ),
                            child: Icon(
                              Icons.diamond,
                              size: 15.sp,
                              color: AppTheme_PUPPII.secondaryColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),

              // Middle right floating element
              Positioned(
                top: 400.h,
                right: 60.w,
                child: Transform.rotate(
                  angle: -0.6,
                  child: Container(
                    width: 38.w,
                    height: 38.w,
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                      borderRadius: BorderRadius.circular(19.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(19.r),
                      child: Image.asset(
                        'assets_puppii/images_puppii/9.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration:
                                AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                              borderRadius: BorderRadius.circular(19.r),
                            ),
                            child: Icon(
                              Icons.category,
                              size: 19.sp,
                              color: AppTheme_PUPPII.accentColor_PUPPII,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
