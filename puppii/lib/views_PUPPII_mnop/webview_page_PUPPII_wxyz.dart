import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';

class WebViewPage_PUPPII_wxyz extends StatefulWidget {
  final String url;
  final String title;

  const WebViewPage_PUPPII_wxyz({
    super.key,
    required this.url,
    required this.title,
  });

  @override
  State<WebViewPage_PUPPII_wxyz> createState() => _WebViewPageState_PUPPII();
}

class _WebViewPageState_PUPPII extends State<WebViewPage_PUPPII_wxyz> {
  late final WebViewController _controller_PUPPII;
  bool _isLoading_PUPPII = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView_PUPPII();
  }

  void _initializeWebView_PUPPII() {
    _controller_PUPPII = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading_PUPPII = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading_PUPPII = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            // Handle error
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme_PUPPII.backgroundColor_PUPPII,
      appBar: AppBar(
        backgroundColor: AppTheme_PUPPII.neumorphismBase_PUPPII,
        elevation: 0,
        leading: Container(
          margin: EdgeInsets.all(8.w),
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12.r),
              onTap: () => Navigator.of(context).pop(),
              child: Icon(
                Icons.arrow_back_ios_new,
                color: AppTheme_PUPPII.primaryColor_PUPPII,
                size: 20.sp,
              ),
            ),
          ),
        ),
        title: Text(
          widget.title,
          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller_PUPPII),
          if (_isLoading_PUPPII)
            Container(
              color: AppTheme_PUPPII.backgroundColor_PUPPII,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: AppTheme_PUPPII.primaryColor_PUPPII,
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      'Loading...',
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        fontSize: 16.sp,
                        color: AppTheme_PUPPII.textSecondary_PUPPII,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
