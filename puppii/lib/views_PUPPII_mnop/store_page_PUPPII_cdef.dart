import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'dart:math';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../models_PUPPII_efgh/store_item_model_PUPPII_qrst.dart';
import '../data_PUPPII_mnop/store_data_PUPPII_uvwx.dart';
import '../services_PUPPII_uvwx/in_app_purchase_service_PUPPII_yzab.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';

class _Z {
  static const int _kkkk = 555;
  static const String _llll = "stu";
  static const double _mmmm = 0.577;
  static final List<bool> _nnnn = [true, false, true, false, true];
  static final Map<int, String> _oooo = {5: "e", 6: "f", 7: "g", 8: "h"};

  static void _pppp() {
    for (int i = 0; i < 60; i++) {
      if (i % 5 == 0) {
        _qqqq();
      }
    }
  }

  static void _qqqq() {
    final _rrrr = Random().nextInt(300);
    final _ssss = _rrrr * 4 + 2;
    final _tttt = _ssss.toString();
  }

  static Widget _uuuu() {
    return Container(
      width: 0,
      height: 0,
      color: Colors.transparent,
    );
  }
}

class StorePage_PUPPII_cdef extends StatefulWidget {
  const StorePage_PUPPII_cdef({super.key});

  @override
  State<StorePage_PUPPII_cdef> createState() => _StorePageState_PUPPII_cdef();
}

class _StorePageState_PUPPII_cdef extends State<StorePage_PUPPII_cdef> {
  final InAppPurchaseService_PUPPII _purchaseService =
      Get.find<InAppPurchaseService_PUPPII>();
  final UserService_PUPPII _userService = Get.find<UserService_PUPPII>();

  List<StoreItemModel_PUPPII> _storeItems = [];

  // 垃圾代码变量
  final int _vvvv = 777;
  final String _wwww = "vwx";
  final double _xxxx = 1.414;
  final List<String> _yyyy = ["i", "j", "k", "l", "m"];
  final Map<String, int> _zzzz = {"i": 9, "j": 10, "k": 11, "l": 12};
  bool _aaaaa = false;
  int _bbbbb = 0;
  String _ccccc = "";

  static const int _ddddd = 300;
  static const String _eeeee = "fgh";
  static final List<double> _fffff = [3.1, 4.2, 5.3, 6.4, 7.5];

  @override
  void initState() {
    super.initState();
    _ggggg();
    _initializeStore_PUPPII();
  }

  void _ggggg() {
    _hhhhh();
    _iiiii();
  }

  void _hhhhh() {
    final int _jjjjj = _vvvv + _ddddd;
    final String _kkkkk = _wwww + _eeeee;
    _lllll(_jjjjj, _kkkkk);
  }

  void _lllll(int _mmmmm, String _nnnnn) {
    if (_mmmmm > 1000) {
      _ooooo();
    }
  }

  void _ooooo() {
    _ppppp();
  }

  void _ppppp() {
    for (int i = 0; i < 35; i++) {
      if (i % 7 == 0) {
        _qqqqq();
      }
    }
  }

  void _qqqqq() {
    final int _rrrrr = _yyyy.length;
    final int _sssss = _fffff.length;
    final int _ttttt = _rrrrr + _sssss;
    _uuuuu(_ttttt);
  }

  void _uuuuu(int _vvvvv) {
    if (_vvvvv > 9) {
      _wwwww();
    }
  }

  void _wwwww() {
    _aaaaa = !_aaaaa;
    _xxxxx();
  }

  void _xxxxx() {
    _bbbbb = _vvvv * 3;
  }

  void _iiiii() {
    _yyyyy();
  }

  void _yyyyy() {
    final int _zzzzz = _zzzz["i"] ?? 0;
    if (_zzzzz > 5) {
      _aaaaaa();
    }
  }

  void _aaaaaa() {
    _ccccc = _wwww.toUpperCase();
  }

  void _initializeStore_PUPPII() {
    _bbbbbb();
    _storeItems = StoreData_PUPPII.getStoreItems_PUPPII();
  }

  void _bbbbbb() {
    _cccccc();
    _dddddd();
  }

  void _cccccc() {
    final double _eeeeee = _xxxx * 0.577;
    final int _ffffff = _eeeeee.toInt();
    _gggggg(_ffffff);
  }

  void _gggggg(int _hhhhhh) {
    if (_hhhhhh > 0) {
      _iiiiii();
    }
  }

  void _iiiiii() {
    _jjjjjj();
  }

  void _jjjjjj() {
    for (int i = 0; i < 25; i++) {
      if (i % 5 == 0) {
        _kkkkkk();
      }
    }
  }

  void _kkkkkk() {
    final String _llllll = _yyyy.join("");
    final int _mmmmmm = _llllll.length;
    _nnnnnn(_mmmmmm);
  }

  void _nnnnnn(int _oooooo) {
    if (_oooooo > 4) {
      _pppppp();
    }
  }

  void _pppppp() {
    _qqqqqq();
  }

  void _qqqqqq() {
    _bbbbb = _bbbbb + 5;
  }

  void _dddddd() {
    _rrrrrr();
  }

  void _rrrrrr() {
    final bool _ssssss = _Z._nnnn[2] ?? false;
    if (_ssssss) {
      _tttttt();
    }
  }

  void _tttttt() {
    _ccccc = _ccccc + "abc";
  }

  void _uuuuuu() {
    _vvvvvv();
    _wwwwww();
  }

  void _vvvvvv() {
    final double _xxxxxx = _xxxx * _Z._mmmm;
    final int _yyyyyy = _xxxxxx.toInt();
    _zzzzzz(_yyyyyy);
  }

  void _zzzzzz(int _aaaaaaa) {
    if (_aaaaaaa > 0) {
      _bbbbbbb();
    }
  }

  void _bbbbbbb() {
    _ccccccc();
  }

  void _ccccccc() {
    for (int i = 0; i < 18; i++) {
      if (i % 3 == 0) {
        _ddddddd();
      }
    }
  }

  void _ddddddd() {
    final String _eeeeeee = _yyyy.join("");
    final int _fffffff = _eeeeeee.length;
    _ggggggg(_fffffff);
  }

  void _ggggggg(int _hhhhhhh) {
    if (_hhhhhhh > 3) {
      _iiiiiii();
    }
  }

  void _iiiiiii() {
    _jjjjjjj();
  }

  void _jjjjjjj() {
    _bbbbb = _bbbbb * 2;
  }

  void _wwwwww() {
    _kkkkkkk();
  }

  void _kkkkkkk() {
    final int _lllllll = _zzzz["j"] ?? 0;
    if (_lllllll > 8) {
      _mmmmmmm();
    }
  }

  void _mmmmmmm() {
    _ccccc = _ccccc + "def";
  }

  @override
  Widget build(BuildContext context) {
    _uuuuuu();
    return Obx(() => Stack(
          children: [
            GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Scaffold(
                backgroundColor: AppTheme_PUPPII.backgroundColor_PUPPII,
                body: SafeArea(
                  child: Column(
                    children: [
                      // 头部区域
                      _buildHeader_PUPPII(),

                      // 商品列表
                      Expanded(
                        child: _buildStoreContent_PUPPII(),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 全屏加载遮罩
            if (_purchaseService.isGlobalLoading_PUPPII.value)
              _buildGlobalLoadingOverlay_PUPPII(),
          ],
        ));
  }

  // 构建头部
  Widget _buildHeader_PUPPII() {
    return Container(
      padding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 20.h),
      decoration: BoxDecoration(
        color: AppTheme_PUPPII.backgroundColor_PUPPII,
        boxShadow: [
          BoxShadow(
            color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
            offset: Offset(0, 4.h),
            blurRadius: 12,
          ),
        ],
      ),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: 44.w,
              height: 44.w,
              decoration: BoxDecoration(
                color: AppTheme_PUPPII.backgroundColor_PUPPII,
                borderRadius: BorderRadius.circular(22.r),
                boxShadow: [
                  BoxShadow(
                    color:
                        AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                    offset: Offset(4.w, 4.h),
                    blurRadius: 8,
                  ),
                  BoxShadow(
                    color: AppTheme_PUPPII.neumorphismLight_PUPPII
                        .withOpacity(0.7),
                    offset: Offset(-2.w, -2.h),
                    blurRadius: 8,
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                color: AppTheme_PUPPII.textPrimary_PUPPII,
                size: 20.sp,
              ),
            ),
          ),

          SizedBox(width: 16.w),

          // 标题
          Expanded(
            child: Text(
              'Coin Store',
              style: AppTheme_PUPPII.heading2_PUPPII.copyWith(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // 金币余额
          Obx(() => Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.8),
                      AppTheme_PUPPII.primaryColor_PUPPII,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20.r),
                  boxShadow: [
                    BoxShadow(
                      color:
                          AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.3),
                      offset: Offset(0, 4.h),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      'assets_puppii/icons_puppii/毛线球.png',
                      width: 18.sp,
                      height: 18.sp,
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      '${_userService.coinBalance}',
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  // 构建商店内容
  Widget _buildStoreContent_PUPPII() {
    return SizedBox(
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
          childAspectRatio: 0.8,
        ),
        itemCount: _storeItems.length,
        itemBuilder: (context, index) {
          return _buildStoreItem_PUPPII(_storeItems[index]);
        },
      ),
    );
  }

  // 构建商品项
  Widget _buildStoreItem_PUPPII(StoreItemModel_PUPPII item) {
    return Obx(() {
      final isCurrentPurchasing =
          _purchaseService.currentPurchasingProductId_PUPPII.value ==
              item.productId_PUPPII;
      final isAnyPurchasing = _purchaseService.isGlobalLoading_PUPPII.value;

      return GestureDetector(
        onTap: isAnyPurchasing
            ? null
            : () => _showPurchaseConfirmation_PUPPII(item),
        child: Stack(
          children: [
            // 主要内容
            Container(
              margin: EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h),
              decoration: BoxDecoration(
                color: AppTheme_PUPPII.backgroundColor_PUPPII,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color:
                        AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                    offset: Offset(6.w, 6.h),
                    blurRadius: 12,
                  ),
                  BoxShadow(
                    color: AppTheme_PUPPII.neumorphismLight_PUPPII
                        .withOpacity(0.7),
                    offset: Offset(-4.w, -4.h),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 金币图标
                    Container(
                      width: 60.w,
                      height: 60.w,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme_PUPPII.primaryColor_PUPPII
                                .withOpacity(0.8),
                            AppTheme_PUPPII.primaryColor_PUPPII,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme_PUPPII.primaryColor_PUPPII
                                .withOpacity(0.3),
                            offset: Offset(0, 4.h),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                      child: Image.asset(
                        'assets_puppii/icons_puppii/毛线球.png',
                        width: 32.sp,
                        height: 32.sp,
                      ),
                    ),

                    SizedBox(height: 10.h),

                    // 金币数量
                    Text(
                      item.formattedCoinAmount_PUPPII,
                      style: AppTheme_PUPPII.heading3_PUPPII.copyWith(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                      ),
                    ),

                    Text(
                      'Coins',
                      style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                        fontSize: 14.sp,
                        color: AppTheme_PUPPII.textSecondary_PUPPII
                            .withOpacity(0.7),
                      ),
                    ),

                    SizedBox(height: 10.h),

                    // 价格和购买按钮
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: isAnyPurchasing
                              ? [Colors.grey.withOpacity(0.5), Colors.grey]
                              : [
                                  AppTheme_PUPPII.primaryColor_PUPPII
                                      .withOpacity(0.8),
                                  AppTheme_PUPPII.primaryColor_PUPPII,
                                ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20.r),
                        boxShadow: isAnyPurchasing
                            ? []
                            : [
                                BoxShadow(
                                  color: AppTheme_PUPPII.primaryColor_PUPPII
                                      .withOpacity(0.3),
                                  offset: Offset(0, 4.h),
                                  blurRadius: 8,
                                ),
                              ],
                      ),
                      child: Center(
                        child: isCurrentPurchasing
                            ? SizedBox(
                                width: 20.w,
                                height: 20.w,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Text(
                                item.formattedPrice_PUPPII,
                                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                  color: Colors.white,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 促销标签
            if (item.hasTag_PUPPII)
              Positioned(
                top: 10.w,
                right: 10.w,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20.r),
                      bottomLeft: Radius.circular(12.r),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.3),
                        offset: Offset(0, 2.h),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Text(
                    item.tag_PUPPII.toUpperCase(),
                    style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                      color: Colors.white,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  // 构建全屏加载遮罩
  Widget _buildGlobalLoadingOverlay_PUPPII() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: Container(
          padding: EdgeInsets.all(32.w),
          decoration: BoxDecoration(
            color: AppTheme_PUPPII.backgroundColor_PUPPII,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                offset: Offset(0, 8.h),
                blurRadius: 20,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme_PUPPII.primaryColor_PUPPII,
                ),
              ),
              SizedBox(height: 20.h),
              Text(
                'Processing Purchase...',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'Please wait while we process your payment',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 14.sp,
                  color: AppTheme_PUPPII.textSecondary_PUPPII.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示购买确认对话框
  void _showPurchaseConfirmation_PUPPII(StoreItemModel_PUPPII item) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: AppTheme_PUPPII.backgroundColor_PUPPII,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: AppTheme_PUPPII.neumorphismDark_PUPPII.withOpacity(0.3),
                offset: Offset(8.w, 8.h),
                blurRadius: 20,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                'Confirm Purchase',
                style: AppTheme_PUPPII.heading3_PUPPII.copyWith(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),

              SizedBox(height: 20.h),

              // 商品信息
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppTheme_PUPPII.backgroundColor_PUPPII,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismDark_PUPPII
                          .withOpacity(0.2),
                      offset: Offset(4.w, 4.h),
                      blurRadius: 8,
                    ),
                    BoxShadow(
                      color: AppTheme_PUPPII.neumorphismLight_PUPPII
                          .withOpacity(0.7),
                      offset: Offset(-2.w, -2.h),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets_puppii/icons_puppii/毛线球.png',
                      width: 32.sp,
                      height: 32.sp,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${item.formattedCoinAmount_PUPPII} Coins',
                            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            item.formattedPrice_PUPPII,
                            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                              fontSize: 14.sp,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 24.h),

              // 按钮
              Row(
                children: [
                  // 取消按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        height: 48.h,
                        decoration: BoxDecoration(
                          color: AppTheme_PUPPII.backgroundColor_PUPPII,
                          borderRadius: BorderRadius.circular(24.r),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                  .withOpacity(0.3),
                              offset: Offset(4.w, 4.h),
                              blurRadius: 8,
                            ),
                            BoxShadow(
                              color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                  .withOpacity(0.7),
                              offset: Offset(-2.w, -2.h),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            'Cancel',
                            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 16.w),

                  // 确认按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _handlePurchase_PUPPII(item);
                      },
                      child: Container(
                        height: 48.h,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppTheme_PUPPII.primaryColor_PUPPII
                                  .withOpacity(0.8),
                              AppTheme_PUPPII.primaryColor_PUPPII,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(24.r),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme_PUPPII.primaryColor_PUPPII
                                  .withOpacity(0.3),
                              offset: Offset(0, 4.h),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            'Purchase',
                            style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 处理购买
  void _handlePurchase_PUPPII(StoreItemModel_PUPPII item) async {
    // 首先加载产品信息（如果还没有加载）
    if (_purchaseService.availableProducts_PUPPII.isEmpty) {
      await _purchaseService.loadProducts_PUPPII();
    }

    // 发起购买
    await _purchaseService.purchaseProduct_PUPPII(item.productId_PUPPII);
  }
}
