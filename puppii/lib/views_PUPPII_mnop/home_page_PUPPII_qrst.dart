import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import '../services_PUPPII_uvwx/in_app_purchase_service_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/character_list_item_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/custom_background_PUPPII_mnop.dart';
import 'splash_page_PUPPII_abcd.dart';

class HomePage_PUPPII extends StatefulWidget {
  const HomePage_PUPPII({super.key});

  @override
  State<HomePage_PUPPII> createState() => _HomePageState_PUPPII();
Puppii Privacy Policy
Last Updated: July 29, 2025
Welcome to the Puppii application (hereinafter referred to as "this Application"). We understand the importance of privacy protection and are committed to safeguarding the security of your personal information. This privacy policy is intended to explain how this Application collects, uses, stores and protects the information you provide during use. Please read this policy carefully before using this Application.
1. Types of Information Collected and Their Purposes
This Application can be used without requiring you to log in or register. During your use of this Application, to facilitate you to provide feedback to us, we may obtain the permissions for your photo album, camera and recording functions:
Photo Album Permission: When you need to upload pictures from your photo album to more clearly describe your feedback, we will obtain your photo album permission to allow you to select and upload relevant pictures. We will only use the pictures you upload for processing your feedback and will not use them for any other purposes.
Camera Permission: If you wish to provide feedback on issues or suggestions by taking real-time photos, we will obtain your camera permission to facilitate your shooting. The photos taken will only be used for handling your feedback matters.
Recording Permission: When you prefer to provide feedback via voice, we will obtain your recording permission to record your voice information. This recording will only be used to understand and handle the content of your feedback.
2. Storage and Protection of Information
We will take reasonable security measures to store and protect the information you provide, preventing information loss, unauthorized access, use, modification or disclosure. We will strictly restrict the scope of personnel who can access your information and require them to fulfill their confidentiality obligations. However, please note that any information transmitted via the Internet may be subject to certain security risks, and we cannot fully guarantee the absolute security of information transmission.
3. Disclosure of Information
We will not disclose your information to any third party except in the following circumstances:
With your explicit consent;
To comply with applicable laws and regulations, legal procedures or government requirements;
To protect the legitimate rights and interests, safety or property of this Application, as well as the safety of users or the public.
4. User Code of Conduct and Handling of Violations
When using this Application to provide feedback, you should ensure that the information and content you provide are legal, true and appropriate, and must not contain any illegal, harmful, insulting, defamatory, pornographic or other inappropriate information.
Content that violates the above regulations will be deleted. At the same time, for users who violate the regulations, we will take measures including but not limited to suspending their use of relevant functions of this Application, and will handle it within 24 hours. We have zero tolerance for any violations to maintain a good application environment.
5. Updates to the Privacy Policy
We may revise this privacy policy in accordance with changes in laws and regulations, updates to application functions, etc. When changes are made to this policy, we will publish the updated privacy policy in this Application and update the "Last Updated" date. Please check this policy regularly to learn about the latest privacy protection measures. Your continued use of this Application indicates that you accept the updated privacy policy.
6. Contact Us
If you have any questions, suggestions or need to make a complaint or report regarding this privacy policy, please contact us through the following method:
<EMAIL>



class _HomePageState_PUPPII extends State<HomePage_PUPPII> {
  final CharacterStateService_PUPPII _characterService_PUPPII =
      Get.put(CharacterStateService_PUPPII());
  final UserService_PUPPII _userService_PUPPII = Get.put(UserService_PUPPII());
  final InAppPurchaseService_PUPPII _purchaseService_PUPPII =
      Get.put(InAppPurchaseService_PUPPII());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkFirstLaunch_PUPPII();
    });
  }

  Future<void> _checkFirstLaunch_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    final isFirstLaunch = prefs.getBool('is_first_launch_puppii') ?? true;

    if (isFirstLaunch && mounted) {
      _showPrivacyDialog_PUPPII();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        body: CustomBackground_PUPPII(
          child: CustomScrollView(
            slivers: [
              // 产品标题和副标题
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 20.h,
                    bottom: 30.h,
                    left: 20.w,
                    right: 20.w,
                  ),
                  child: Column(
                    children: [
                      // 产品标题
                      Text(
                        'Puppii',
                        style: GoogleFonts.comfortaa(
                          fontSize: 38.sp,
                          fontWeight: FontWeight.w900,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                          letterSpacing: 2.0,
                        ),
                      ),

                      SizedBox(height: 8.h),

                      // 副标题
                      Text(
                        'Your AI Woven Coaster Designers',
                        style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                          fontSize: 14.sp,
                          color: AppTheme_PUPPII.textSecondary_PUPPII,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // 角色列表
              Obx(() {
                if (_characterService_PUPPII.isLoading_PUPPII.value) {
                  return SliverToBoxAdapter(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.all(50.h),
                        child: CircularProgressIndicator(
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      ),
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final character =
                          _characterService_PUPPII.characters_PUPPII[index];
                      final isLeftAligned = index % 2 == 0; // 偶数索引左对齐，奇数索引右对齐

                      return CharacterListItem_PUPPII(
                        character_PUPPII: character,
                        isLeftAligned_PUPPII: isLeftAligned,
                        onTap_PUPPII: () {
                          // 点击卡片区域的回调（可以用于查看角色详情等）
                          print('Tapped on ${character.nickname_PUPPII} card');
                        },
                      );
                    },
                    childCount:
                        _characterService_PUPPII.characters_PUPPII.length,
                  ),
                );
              }),

              // 底部空间，为导航栏留空
              SliverToBoxAdapter(
                child: SizedBox(height: 120.h),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPrivacyDialog_PUPPII() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            // 返回到启动页
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                  builder: (context) => const SplashPage_PUPPII_abcd()),
              (route) => false,
            );
            return false;
          },
          child: Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: 320.w,
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(20.w),
                    decoration: BoxDecoration(
                      color:
                          AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(20.r),
                      ),
                    ),
                    child: Text(
                      'Privacy Policy & Terms of Service',
                      style: GoogleFonts.poppins(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w700,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // 协议内容
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.all(20.w),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Privacy Policy',
                              style: GoogleFonts.poppins(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            ),
                            SizedBox(height: 12.h),
                            Text(
                              'Last updated: [Date]\n\n'
                              'This Privacy Policy describes Our policies and procedures on the collection, use and disclosure of Your information when You use the Service and tells You about Your privacy rights and how the law protects You.\n\n'
                              'We use Your Personal data to provide and improve the Service. By using the Service, You agree to the collection and use of information in accordance with this Privacy Policy.\n\n'
                              'Interpretation and Definitions\n\n'
                              'Interpretation\n'
                              'The words of which the initial letter is capitalized have meanings defined under the following conditions. The following definitions shall have the same meaning regardless of whether they appear in singular or in plural.\n\n'
                              'Definitions\n'
                              'For the purposes of this Privacy Policy:\n'
                              '• Account means a unique account created for You to access our Service or parts of our Service.\n'
                              '• Company (referred to as either "the Company", "we", "us" or "our") refers to [Company Name].\n'
                              '• Cookies are small files that are placed on Your computer, mobile device or any other device by a website, containing the details of Your browsing history on that website among its many uses.\n'
                              '• Country refers to: [Country]\n'
                              '• Device means any device that can access the Service such as a computer, a cellphone or a digital tablet.\n'
                              '• Personal Data is any information that relates to an identified or identifiable individual.\n'
                              '• Service refers to the Application.\n'
                              '• Service Provider means any natural or legal person who processes the data on behalf of the Company.\n'
                              '• Usage Data refers to data collected automatically, either generated by the use of the Service or from the Service infrastructure itself.\n'
                              '• You means the individual accessing or using the Service, or the company, or other legal entity on behalf of which such individual is accessing or using the Service, as applicable.',
                              style: GoogleFonts.poppins(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w400,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                                height: 1.5,
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Text(
                              'Terms of Service',
                              style: GoogleFonts.poppins(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            ),
                            SizedBox(height: 12.h),
                            Text(
                              'Last updated: [Date]\n\n'
                              'These Terms of Service ("Terms", "Terms of Service") govern your relationship with [App Name] mobile application (the "Service") operated by [Company Name] ("us", "we", or "our").\n\n'
                              'Please read these Terms of Service carefully before using our Service.\n\n'
                              'Your access to and use of the Service is conditioned on your acceptance of and compliance with these Terms. These Terms apply to all visitors, users and others who access or use the Service.\n\n'
                              'By accessing or using our Service you agree to be bound by these Terms. If you disagree with any part of the terms then you may not access the Service.\n\n'
                              'Accounts\n'
                              'When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.\n\n'
                              'Prohibited Uses\n'
                              'You may not use our Service:\n'
                              '• For any unlawful purpose or to solicit others to perform unlawful acts\n'
                              '• To violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances\n'
                              '• To infringe upon or violate our intellectual property rights or the intellectual property rights of others\n'
                              '• To harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate\n'
                              '• To submit false or misleading information\n\n'
                              'Termination\n'
                              'We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation.',
                              style: GoogleFonts.poppins(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w400,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 同意按钮
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(20.w),
                    child: GestureDetector(
                      onTap: () async {
                        // 标记为已同意
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('is_first_launch_puppii', false);

                        if (mounted) {
                          Navigator.of(context).pop();
                          // 确保关闭弹窗时不让任何输入框获取焦点
                          FocusScope.of(context).unfocus();
                        }
                      },
                      child: Container(
                        height: 50.h,
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(25.r),
                          color: AppTheme_PUPPII.primaryColor_PUPPII
                              .withOpacity(0.1),
                        ),
                        child: Center(
                          child: Text(
                            'I Agree',
                            style: GoogleFonts.poppins(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
