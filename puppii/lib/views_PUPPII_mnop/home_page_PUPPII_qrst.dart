import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import '../services_PUPPII_uvwx/in_app_purchase_service_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/character_list_item_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/custom_background_PUPPII_mnop.dart';
import 'splash_page_PUPPII_abcd.dart';

class HomePage_PUPPII extends StatefulWidget {
  const HomePage_PUPPII({super.key});

  @override
  State<HomePage_PUPPII> createState() => _HomePageState_PUPPII();
}

class _HomePageState_PUPPII extends State<HomePage_PUPPII> {
  final CharacterStateService_PUPPII _characterService_PUPPII =
      Get.put(CharacterStateService_PUPPII());
  final UserService_PUPPII _userService_PUPPII = Get.put(UserService_PUPPII());
  final InAppPurchaseService_PUPPII _purchaseService_PUPPII =
      Get.put(InAppPurchaseService_PUPPII());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkFirstLaunch_PUPPII();
    });
  }

  Future<void> _checkFirstLaunch_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    final isFirstLaunch = prefs.getBool('is_first_launch_puppii') ?? true;

    if (isFirstLaunch && mounted) {
      _showPrivacyDialog_PUPPII();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        body: CustomBackground_PUPPII(
          child: CustomScrollView(
            slivers: [
              // 产品标题和副标题
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 20.h,
                    bottom: 30.h,
                    left: 20.w,
                    right: 20.w,
                  ),
                  child: Column(
                    children: [
                      // 产品标题
                      Text(
                        'Puppii',
                        style: GoogleFonts.comfortaa(
                          fontSize: 38.sp,
                          fontWeight: FontWeight.w900,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                          letterSpacing: 2.0,
                        ),
                      ),

                      SizedBox(height: 8.h),

                      // 副标题
                      Text(
                        'Your AI Woven Coaster Designers',
                        style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                          fontSize: 14.sp,
                          color: AppTheme_PUPPII.textSecondary_PUPPII,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // 角色列表
              Obx(() {
                if (_characterService_PUPPII.isLoading_PUPPII.value) {
                  return SliverToBoxAdapter(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.all(50.h),
                        child: CircularProgressIndicator(
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      ),
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final character =
                          _characterService_PUPPII.characters_PUPPII[index];
                      final isLeftAligned = index % 2 == 0; // 偶数索引左对齐，奇数索引右对齐

                      return CharacterListItem_PUPPII(
                        character_PUPPII: character,
                        isLeftAligned_PUPPII: isLeftAligned,
                        onTap_PUPPII: () {
                          // 点击卡片区域的回调（可以用于查看角色详情等）
                          print('Tapped on ${character.nickname_PUPPII} card');
                        },
                      );
                    },
                    childCount:
                        _characterService_PUPPII.characters_PUPPII.length,
                  ),
                );
              }),

              // 底部空间，为导航栏留空
              SliverToBoxAdapter(
                child: SizedBox(height: 120.h),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPrivacyDialog_PUPPII() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            // 返回到启动页
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                  builder: (context) => const SplashPage_PUPPII_abcd()),
              (route) => false,
            );
            return false;
          },
          child: Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: 320.w,
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(20.w),
                    decoration: BoxDecoration(
                      color:
                          AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(20.r),
                      ),
                    ),
                    child: Text(
                      'Privacy Policy & Terms of Service',
                      style: GoogleFonts.poppins(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w700,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // 协议内容
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.all(20.w),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Privacy Policy',
                              style: GoogleFonts.poppins(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            ),
                            SizedBox(height: 12.h),
                            Text(
                              'We respect your privacy and are committed to protecting your personal data. This privacy policy explains how we collect, use, and safeguard your information when you use our Puppii app.\n\n'
                              'Information We Collect:\n'
                              '• App usage data and preferences\n'
                              '• Device information for optimization\n'
                              '• User-generated content and designs\n\n'
                              'How We Use Your Information:\n'
                              '• To provide and improve our services\n'
                              '• To personalize your experience\n'
                              '• To communicate with you about updates\n\n'
                              'Data Security:\n'
                              'We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
                              style: GoogleFonts.poppins(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w400,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                                height: 1.5,
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Text(
                              'Terms of Service',
                              style: GoogleFonts.poppins(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            ),
                            SizedBox(height: 12.h),
                            Text(
                              'By using Puppii, you agree to these terms of service.\n\n'
                              'Acceptable Use:\n'
                              '• Use the app for lawful purposes only\n'
                              '• Respect intellectual property rights\n'
                              '• Do not attempt to reverse engineer the app\n\n'
                              'User Content:\n'
                              '• You retain ownership of your designs\n'
                              '• You grant us license to display your content within the app\n'
                              '• You are responsible for the content you create\n\n'
                              'Limitation of Liability:\n'
                              'The app is provided "as is" without warranties. We are not liable for any damages arising from your use of the app.\n\n'
                              'Changes to Terms:\n'
                              'We may update these terms from time to time. Continued use of the app constitutes acceptance of updated terms.',
                              style: GoogleFonts.poppins(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w400,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 同意按钮
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(20.w),
                    child: GestureDetector(
                      onTap: () async {
                        // 标记为已同意
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('is_first_launch_puppii', false);

                        if (mounted) {
                          Navigator.of(context).pop();
                          // 确保关闭弹窗时不让任何输入框获取焦点
                          FocusScope.of(context).unfocus();
                        }
                      },
                      child: Container(
                        height: 50.h,
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(25.r),
                          color: AppTheme_PUPPII.primaryColor_PUPPII
                              .withOpacity(0.1),
                        ),
                        child: Center(
                          child: Text(
                            'I Agree',
                            style: GoogleFonts.poppins(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
