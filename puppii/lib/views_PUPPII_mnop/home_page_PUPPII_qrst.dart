import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import '../services_PUPPII_uvwx/in_app_purchase_service_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/character_list_item_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/custom_background_PUPPII_mnop.dart';

class HomePage_PUPPII extends StatefulWidget {
  const HomePage_PUPPII({super.key});

  @override
  State<HomePage_PUPPII> createState() => _HomePageState_PUPPII();
}

class _HomePageState_PUPPII extends State<HomePage_PUPPII> {
  final CharacterStateService_PUPPII _characterService_PUPPII =
      Get.put(CharacterStateService_PUPPII());
  final UserService_PUPPII _userService_PUPPII = Get.put(UserService_PUPPII());
  final InAppPurchaseService_PUPPII _purchaseService_PUPPII =
      Get.put(InAppPurchaseService_PUPPII());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        body: CustomBackground_PUPPII(
          child: CustomScrollView(
            slivers: [
              // 产品标题和副标题
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 20.h,
                    bottom: 30.h,
                    left: 20.w,
                    right: 20.w,
                  ),
                  child: Column(
                    children: [
                      // 产品标题
                      Text(
                        'Puppii',
                        style: GoogleFonts.comfortaa(
                          fontSize: 38.sp,
                          fontWeight: FontWeight.w900,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                          letterSpacing: 2.0,
                        ),
                      ),

                      SizedBox(height: 8.h),

                      // 副标题
                      Text(
                        'Your AI Woven Coaster Designers',
                        style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                          fontSize: 14.sp,
                          color: AppTheme_PUPPII.textSecondary_PUPPII,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // 角色列表
              Obx(() {
                if (_characterService_PUPPII.isLoading_PUPPII.value) {
                  return SliverToBoxAdapter(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.all(50.h),
                        child: CircularProgressIndicator(
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      ),
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final character =
                          _characterService_PUPPII.characters_PUPPII[index];
                      final isLeftAligned = index % 2 == 0; // 偶数索引左对齐，奇数索引右对齐

                      return CharacterListItem_PUPPII(
                        character_PUPPII: character,
                        isLeftAligned_PUPPII: isLeftAligned,
                        onTap_PUPPII: () {
                          // 点击卡片区域的回调（可以用于查看角色详情等）
                          print('Tapped on ${character.nickname_PUPPII} card');
                        },
                      );
                    },
                    childCount:
                        _characterService_PUPPII.characters_PUPPII.length,
                  ),
                );
              }),

              // 底部空间，为导航栏留空
              SliverToBoxAdapter(
                child: SizedBox(height: 120.h),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
