import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import '../services_PUPPII_uvwx/in_app_purchase_service_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/character_list_item_PUPPII_yzab.dart';
import '../widgets_PUPPII_klmn/custom_background_PUPPII_mnop.dart';
import 'splash_page_PUPPII_abcd.dart';

class HomePage_PUPPII extends StatefulWidget {
  const HomePage_PUPPII({super.key});

  @override
  State<HomePage_PUPPII> createState() => _HomePageState_PUPPII();
}

class _HomePageState_PUPPII extends State<HomePage_PUPPII> {
  final CharacterStateService_PUPPII _characterService_PUPPII =
      Get.put(CharacterStateService_PUPPII());
  final UserService_PUPPII _userService_PUPPII = Get.put(UserService_PUPPII());
  final InAppPurchaseService_PUPPII _purchaseService_PUPPII =
      Get.put(InAppPurchaseService_PUPPII());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkFirstLaunch_PUPPII();
    });
  }

  Future<void> _checkFirstLaunch_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    final isFirstLaunch = prefs.getBool('is_first_launch_puppii') ?? true;

    if (isFirstLaunch && mounted) {
      _showPrivacyDialog_PUPPII();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        body: CustomBackground_PUPPII(
          child: CustomScrollView(
            slivers: [
              // 产品标题和副标题
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 20.h,
                    bottom: 30.h,
                    left: 20.w,
                    right: 20.w,
                  ),
                  child: Column(
                    children: [
                      // 产品标题
                      Text(
                        'Puppii',
                        style: GoogleFonts.comfortaa(
                          fontSize: 38.sp,
                          fontWeight: FontWeight.w900,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                          letterSpacing: 2.0,
                        ),
                      ),

                      SizedBox(height: 8.h),

                      // 副标题
                      Text(
                        'Your AI Woven Coaster Designers',
                        style: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                          fontSize: 14.sp,
                          color: AppTheme_PUPPII.textSecondary_PUPPII,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // 角色列表
              Obx(() {
                if (_characterService_PUPPII.isLoading_PUPPII.value) {
                  return SliverToBoxAdapter(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.all(50.h),
                        child: CircularProgressIndicator(
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      ),
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final character =
                          _characterService_PUPPII.characters_PUPPII[index];
                      final isLeftAligned = index % 2 == 0; // 偶数索引左对齐，奇数索引右对齐

                      return CharacterListItem_PUPPII(
                        character_PUPPII: character,
                        isLeftAligned_PUPPII: isLeftAligned,
                        onTap_PUPPII: () {
                          // 点击卡片区域的回调（可以用于查看角色详情等）
                          print('Tapped on ${character.nickname_PUPPII} card');
                        },
                      );
                    },
                    childCount:
                        _characterService_PUPPII.characters_PUPPII.length,
                  ),
                );
              }),

              // 底部空间，为导航栏留空
              SliverToBoxAdapter(
                child: SizedBox(height: 120.h),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPrivacyDialog_PUPPII() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            // 返回到启动页
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                  builder: (context) => const SplashPage_PUPPII_abcd()),
              (route) => false,
            );
            return false;
          },
          child: Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: 320.w,
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(20.w),
                    decoration: BoxDecoration(
                      color:
                          AppTheme_PUPPII.primaryColor_PUPPII.withOpacity(0.1),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(20.r),
                      ),
                    ),
                    child: Text(
                      'Privacy Policy & Terms of Service',
                      style: GoogleFonts.poppins(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w700,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // 协议内容
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.all(20.w),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Puppii Privacy Policy',
                              style: GoogleFonts.poppins(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            ),
                            SizedBox(height: 12.h),
                            Text(
                              'Last Updated: July 29, 2025\n\n'
                              'Welcome to the Puppii application (hereinafter referred to as "this Application"). We understand the importance of privacy protection and are committed to safeguarding the security of your personal information. This privacy policy is intended to explain how this Application collects, uses, stores and protects the information you provide during use. Please read this policy carefully before using this Application.\n\n'
                              '1. Types of Information Collected and Their Purposes\n'
                              'This Application can be used without requiring you to log in or register. During your use of this Application, to facilitate you to provide feedback to us, we may obtain the permissions for your photo album, camera and recording functions:\n\n'
                              'Photo Album Permission: When you need to upload pictures from your photo album to more clearly describe your feedback, we will obtain your photo album permission to allow you to select and upload relevant pictures. We will only use the pictures you upload for processing your feedback and will not use them for any other purposes.\n\n'
                              'Camera Permission: If you wish to provide feedback on issues or suggestions by taking real-time photos, we will obtain your camera permission to facilitate your shooting. The photos taken will only be used for handling your feedback matters.\n\n'
                              'Recording Permission: When you prefer to provide feedback via voice, we will obtain your recording permission to record your voice information. This recording will only be used to understand and handle the content of your feedback.\n\n'
                              '2. Storage and Protection of Information\n'
                              'We will take reasonable security measures to store and protect the information you provide, preventing information loss, unauthorized access, use, modification or disclosure. We will strictly restrict the scope of personnel who can access your information and require them to fulfill their confidentiality obligations. However, please note that any information transmitted via the Internet may be subject to certain security risks, and we cannot fully guarantee the absolute security of information transmission.\n\n'
                              '3. Disclosure of Information\n'
                              'We will not disclose your information to any third party except in the following circumstances:\n'
                              '• With your explicit consent;\n'
                              '• To comply with applicable laws and regulations, legal procedures or government requirements;\n'
                              '• To protect the legitimate rights and interests, safety or property of this Application, as well as the safety of users or the public.\n\n'
                              '4. User Code of Conduct and Handling of Violations\n'
                              'When using this Application to provide feedback, you should ensure that the information and content you provide are legal, true and appropriate, and must not contain any illegal, harmful, insulting, defamatory, pornographic or other inappropriate information.\n\n'
                              'Content that violates the above regulations will be deleted. At the same time, for users who violate the regulations, we will take measures including but not limited to suspending their use of relevant functions of this Application, and will handle it within 24 hours. We have zero tolerance for any violations to maintain a good application environment.\n\n'
                              '5. Updates to the Privacy Policy\n'
                              'We may revise this privacy policy in accordance with changes in laws and regulations, updates to application functions, etc. When changes are made to this policy, we will publish the updated privacy policy in this Application and update the "Last Updated" date. Please check this policy regularly to learn about the latest privacy protection measures. Your continued use of this Application indicates that you accept the updated privacy policy.\n\n'
                              '6. Contact Us\n'
                              'If you have any questions, suggestions or need to make a complaint or report regarding this privacy policy, please contact us through the following method:\n'
                              '<EMAIL>',
                              style: GoogleFonts.poppins(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w400,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                                height: 1.5,
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Text(
                              'Puppii User Agreement',
                              style: GoogleFonts.poppins(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppTheme_PUPPII.primaryColor_PUPPII,
                              ),
                            ),
                            SizedBox(height: 12.h),
                            Text(
                              'Last Updated: July 29, 2025\n\n'
                              'Welcome to the Puppii application (hereinafter referred to as "this Application"). This agreement is a legally binding agreement between you and the operator of this Application regarding the use of this Application. Please read this agreement carefully before using this Application. Once you use this Application, it means that you have fully understood and agreed to all the contents of this agreement.\n\n'
                              '1. Application Usage Specifications\n'
                              'You have the right to use all functions provided by this Application on the premise of complying with this agreement and relevant laws and regulations, including but not limited to using services such as AI weaving design suggestions, pattern creation, color matching and weaving skill guidance provided by this Application.\n\n'
                              'In the process of using this Application, you shall ensure that your actions are legal and compliant, and shall not use this Application to engage in any illegal or irregular activities, interfere with the normal operation of this Application, or damage the legitimate rights and interests of other users or third parties.\n\n'
                              '2. Intellectual Property Statement\n'
                              'All contents in this Application, including but not limited to texts, pictures, audios, videos, software, programs, design schemes, patterns, etc., their intellectual property rights belong to the operator of this Application or relevant right holders.\n\n'
                              'Without the written permission of the operator of this Application, you shall not copy, disseminate, adapt, translate, compile, reverse engineer, crack or conduct any other form of use or processing of any content in this Application. You have the legal right to use the weaving design schemes and other contents generated through this Application, but the operator of this Application reserves the reasonable right to display and promote such contents in this Application.\n\n'
                              '3. User Feedback and Handling\n'
                              'You can submit feedback through the feedback channels provided by this Application, including but not limited to using the album, camera and recording functions. The feedback content you submit shall be true, legal and effective, and shall not contain any illegal, infringing, vulgar, defamatory or other bad information.\n\n'
                              'The operator of this Application has the right to handle and respond to the feedback you submit according to the actual situation. If the feedback content you submit violates relevant regulations, the operator of this Application has the right to take measures such as deleting the content and restricting the use of relevant functions, and shall not bear any responsibility.\n\n'
                              '4. Limitation of Liability and Disclaimer\n'
                              'The operator of this Application tries its best to ensure the normal operation of this Application and the accuracy of the information provided, but does not make any express or implied guarantee for the uninterrupted operation, error-free operation of this Application and the absolute accuracy of the information.\n\n'
                              'The operator of this Application shall not be liable for any loss caused by factors beyond its control such as force majeure, network failure, third-party service problems, etc., which prevent you from using this Application or cause you losses. The operator of this Application shall not be liable for any loss incurred by you due to the use of this Application or reliance on the information provided by this Application, unless such loss is caused by the intentional or gross negligence of the operator of this Application.\n\n'
                              '5. Modification and Termination of the Agreement\n'
                              'The operator of this Application has the right to modify this agreement according to changes in laws and regulations, business development needs, etc. The modified agreement will be published in this Application, and will be binding on you after publication. If you do not agree with the modified agreement, you shall stop using this Application immediately; if you continue to use this Application, it is deemed that you accept the modified agreement.\n\n'
                              'You can stop using this Application at any time to terminate this agreement. If you violate the provisions of this agreement, the operator of this Application has the right to terminate your right to use this Application at any time without prior notice to you.\n\n'
                              '6. Other Terms\n'
                              'The conclusion, validity, interpretation, performance and dispute resolution of this agreement shall be governed by relevant laws and regulations. Any dispute arising from or in connection with this agreement shall first be resolved through friendly negotiation between the parties; if negotiation fails, either party shall have the right to bring a lawsuit to the competent court.\n\n'
                              'This agreement constitutes the complete agreement between you and the operator of this Application regarding the use of this Application, and replaces all previous oral or written agreements.\n\n'
                              'If you have any questions about this agreement, you can contact <NAME_EMAIL>.',
                              style: GoogleFonts.poppins(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w400,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 同意按钮
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(20.w),
                    child: GestureDetector(
                      onTap: () async {
                        // 标记为已同意
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('is_first_launch_puppii', false);

                        if (mounted) {
                          Navigator.of(context).pop();
                          // 确保关闭弹窗时不让任何输入框获取焦点
                          FocusScope.of(context).unfocus();
                        }
                      },
                      child: Container(
                        height: 50.h,
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(25.r),
                          color: AppTheme_PUPPII.primaryColor_PUPPII
                              .withOpacity(0.1),
                        ),
                        child: Center(
                          child: Text(
                            'I Agree',
                            style: GoogleFonts.poppins(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
