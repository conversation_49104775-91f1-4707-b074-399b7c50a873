import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme_PUPPII_abcd/app_theme_PUPPII.dart';
import '../services_PUPPII_uvwx/user_service_PUPPII_mnop.dart';
import '../services_PUPPII_uvwx/character_state_service_PUPPII_qrst.dart';
import '../widgets_PUPPII_klmn/custom_background_PUPPII_mnop.dart';
import '../widgets_PUPPII_klmn/favorite_character_card_PUPPII_wxyz.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../models_PUPPII_efgh/user_model_PUPPII_wxyz.dart';
import '../widgets_PUPPII_klmn/soul_question_card_PUPPII_efgh.dart';
import 'feedback_page_PUPPII_abcd.dart';
import 'store_page_PUPPII_cdef.dart';
import 'webview_page_PUPPII_wxyz.dart';

class ProfilePage_PUPPII extends StatefulWidget {
  const ProfilePage_PUPPII({super.key});

  @override
  State<ProfilePage_PUPPII> createState() => _ProfilePageState_PUPPII();
}

class _ProfilePageState_PUPPII extends State<ProfilePage_PUPPII> {
  final UserService_PUPPII _userService_PUPPII = Get.find<UserService_PUPPII>();
  final CharacterStateService_PUPPII _characterService_PUPPII =
      Get.find<CharacterStateService_PUPPII>();

  List<CharacterModel_PUPPII> _favoriteCharacters_PUPPII = [];

  @override
  void initState() {
    super.initState();
    _loadFavoriteCharacters_PUPPII();

    // 监听用户数据变化
    ever(_userService_PUPPII.currentUser_PUPPII, (_) {
      _loadFavoriteCharacters_PUPPII();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次页面重新显示时刷新收藏列表
    _loadFavoriteCharacters_PUPPII();
  }

  void _loadFavoriteCharacters_PUPPII() {
    final user = _userService_PUPPII.user;
    if (user != null) {
      final favoriteIds = user.favoriteCharacterIds_PUPPII;
      print('[ProfilePage] Loading favorites: $favoriteIds');
      _favoriteCharacters_PUPPII = _characterService_PUPPII.characters_PUPPII
          .where((character) => favoriteIds.contains(character.id_PUPPII))
          .toList();
      print(
          '[ProfilePage] Found ${_favoriteCharacters_PUPPII.length} favorite characters');
      if (mounted) {
        setState(() {});
      }
    } else {
      print('[ProfilePage] User is null');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        body: CustomBackground_PUPPII(
          child: CustomScrollView(
            slivers: [
              // 页面标题和编辑按钮
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.fromLTRB(20.w, 60.h, 20.w, 20.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Profile',
                        style: GoogleFonts.comfortaa(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w700,
                          color: AppTheme_PUPPII.textPrimary_PUPPII,
                        ),
                      ),
                      Container(
                        width: 32.w,
                        height: 32.w,
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(16.r),
                            onTap: _showEditDialog_PUPPII,
                            child: Icon(
                              Icons.edit,
                              color: AppTheme_PUPPII.primaryColor_PUPPII,
                              size: 16.sp,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 用户信息卡片
              SliverToBoxAdapter(
                child: Obx(() {
                  final user = _userService_PUPPII.currentUser_PUPPII.value;
                  if (user == null) {
                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 20.w),
                      height: 200.h,
                      child: const Center(child: CircularProgressIndicator()),
                    );
                  }

                  _userService_PUPPII.getUserStats_PUPPII();

                  return Container(
                    margin:
                        EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                    padding: EdgeInsets.all(24.w),
                    decoration: AppTheme_PUPPII.neumorphismCard_PUPPII,
                    child: Column(
                      children: [
                        // 头像
                        Container(
                          width: 100.w,
                          height: 100.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                    .withOpacity(0.4),
                                offset: Offset(4.w, 4.h),
                                blurRadius: 10,
                              ),
                              BoxShadow(
                                color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                    .withOpacity(0.9),
                                offset: Offset(-3.w, -3.h),
                                blurRadius: 8,
                              ),
                            ],
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              user.avatarPath_PUPPII,
                              width: 100.w,
                              height: 100.w,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppTheme_PUPPII.primaryColor_PUPPII
                                        .withOpacity(0.1),
                                  ),
                                  child: Icon(
                                    Icons.person,
                                    size: 50.sp,
                                    color: AppTheme_PUPPII.primaryColor_PUPPII,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),

                        SizedBox(height: 20.h),

                        // 用户名
                        Text(
                          user.nickname_PUPPII,
                          style: AppTheme_PUPPII.heading3_PUPPII.copyWith(
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),

                        SizedBox(height: 16.h),

                        // 金币余额
                        Center(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 8.h,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme_PUPPII.backgroundColor_PUPPII,
                              borderRadius: BorderRadius.circular(20.r),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme_PUPPII.neumorphismDark_PUPPII
                                      .withOpacity(0.3),
                                  offset: Offset(2.w, 2.h),
                                  blurRadius: 4,
                                ),
                                BoxShadow(
                                  color: AppTheme_PUPPII.neumorphismLight_PUPPII
                                      .withOpacity(0.8),
                                  offset: Offset(-1.w, -1.h),
                                  blurRadius: 3,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Image.asset(
                                  'assets_puppii/icons_puppii/毛线球.png',
                                  width: 20.w,
                                  height: 20.w,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Icon(
                                      Icons.monetization_on,
                                      size: 20.sp,
                                      color:
                                          AppTheme_PUPPII.primaryColor_PUPPII,
                                    );
                                  },
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  '${user.coinBalance_PUPPII}',
                                  style:
                                      AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme_PUPPII.textPrimary_PUPPII,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),

              // 收藏角色标题
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.fromLTRB(20.w, 30.h, 20.w, 16.h),
                  child: Text(
                    'Favorite Characters',
                    style: GoogleFonts.comfortaa(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w600,
                      color: AppTheme_PUPPII.textPrimary_PUPPII,
                    ),
                  ),
                ),
              ),

              // 收藏角色列表
              SliverToBoxAdapter(
                child: _favoriteCharacters_PUPPII.isEmpty
                    ? Container(
                        margin: EdgeInsets.symmetric(horizontal: 20.w),
                        padding: EdgeInsets.all(40.w),
                        decoration: AppTheme_PUPPII.neumorphismCard_PUPPII,
                        child: Column(
                          children: [
                            Icon(
                              Icons.favorite_border,
                              size: 48.sp,
                              color: AppTheme_PUPPII.textSecondary_PUPPII,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              'No Favorite Characters',
                              style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Add characters to favorites from the home page',
                              style:
                                  AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                                fontSize: 12.sp,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    : Container(
                        height: 200.h,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          itemCount: _favoriteCharacters_PUPPII.length,
                          itemBuilder: (context, index) {
                            return FavoriteCharacterCard_PUPPII(
                              character_PUPPII:
                                  _favoriteCharacters_PUPPII[index],
                              onUnfavorite_PUPPII: () async {
                                // 等待一小段时间确保UserService状态已更新
                                await Future.delayed(
                                    const Duration(milliseconds: 100));
                                _loadFavoriteCharacters_PUPPII();
                              },
                            );
                          },
                        ),
                      ),
              ),

              // 菜单项
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.fromLTRB(20.w, 30.h, 20.w, 16.h),
                  child: Column(
                    children: [
                      _buildMenuItem_PUPPII(
                        icon: Icons.store_outlined,
                        title: 'Coin Store',
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const StorePage_PUPPII_cdef(),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 16.h),
                      _buildMenuItem_PUPPII(
                        icon: Icons.feedback_outlined,
                        title: 'Feedback',
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const FeedbackPage_PUPPII_abcd(),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 16.h),
                      _buildMenuItem_PUPPII(
                        icon: Icons.info_outline,
                        title: 'About',
                        onTap: () {
                          _showAboutDialog_PUPPII();
                        },
                      ),
                    ],
                  ),
                ),
              ),

              // 底部空间，为导航栏留空
              SliverToBoxAdapter(
                child: SizedBox(height: 120.h),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem_PUPPII({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      height: 60.h,
      decoration: AppTheme_PUPPII.neumorphismElevated_PUPPII,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20.r),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppTheme_PUPPII.primaryColor_PUPPII,
                  size: 24.sp,
                ),
                SizedBox(width: 16.w),
                Text(
                  title,
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 16.sp,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme_PUPPII.textLight_PUPPII,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showEditDialog_PUPPII() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Edit Profile',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),

              // 修改头像按钮
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  _showAvatarDialog_PUPPII();
                },
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.photo_camera,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                        size: 20.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Change Avatar',
                        style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 12.h),

              // 修改昵称按钮
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  _showNicknameDialog_PUPPII();
                },
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.edit,
                        color: AppTheme_PUPPII.primaryColor_PUPPII,
                        size: 20.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Change Nickname',
                        style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAvatarDialog_PUPPII() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Choose Avatar',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),

              // 头像网格
              GridView.builder(
                shrinkWrap: true,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 12.w,
                  mainAxisSpacing: 12.h,
                ),
                itemCount: 6,
                itemBuilder: (context, index) {
                  final avatarPath =
                      'assets_puppii/images_puppii/user_0${index + 1}.png';
                  return GestureDetector(
                    onTap: () async {
                      final user = _userService_PUPPII.user;
                      if (user != null) {
                        final updatedUser = UserModel_PUPPII(
                          id_PUPPII: user.id_PUPPII,
                          nickname_PUPPII: user.nickname_PUPPII,
                          avatarPath_PUPPII: avatarPath,
                          coinBalance_PUPPII: user.coinBalance_PUPPII,
                          favoriteCharacterIds_PUPPII:
                              user.favoriteCharacterIds_PUPPII,
                        );
                        await _userService_PUPPII
                            .updateUser_PUPPII(updatedUser);
                      }
                      if (mounted) {
                        Navigator.of(context).pop();
                      }
                    },
                    child: Container(
                      decoration:
                          AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: Image.asset(
                          avatarPath,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: AppTheme_PUPPII.neumorphismCard_PUPPII
                                  .copyWith(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Icon(
                                Icons.person,
                                size: 30.sp,
                                color: AppTheme_PUPPII.textSecondary_PUPPII,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showNicknameDialog_PUPPII() {
    final TextEditingController nicknameController = TextEditingController();
    final user = _userService_PUPPII.user;
    if (user != null) {
      nicknameController.text = user.nickname_PUPPII;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Change Nickname',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),

              // 输入框
              Container(
                decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: TextField(
                  controller: nicknameController,
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 16.sp,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter new nickname',
                    hintStyle: AppTheme_PUPPII.bodyTextSmall_PUPPII.copyWith(
                      color: AppTheme_PUPPII.textSecondary_PUPPII,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(16.w),
                  ),
                  onSubmitted: (value) async {
                    if (value.trim().isNotEmpty && user != null) {
                      final updatedUser = UserModel_PUPPII(
                        id_PUPPII: user.id_PUPPII,
                        nickname_PUPPII: value.trim(),
                        avatarPath_PUPPII: user.avatarPath_PUPPII,
                        coinBalance_PUPPII: user.coinBalance_PUPPII,
                        favoriteCharacterIds_PUPPII:
                            user.favoriteCharacterIds_PUPPII,
                      );
                      await _userService_PUPPII.updateUser_PUPPII(updatedUser);
                    }
                    if (mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ),

              SizedBox(height: 16.h),

              // 按钮
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          'Cancel',
                          textAlign: TextAlign.center,
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                            color: AppTheme_PUPPII.textSecondary_PUPPII,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: GestureDetector(
                      onTap: () async {
                        final value = nicknameController.text.trim();
                        if (value.isNotEmpty && user != null) {
                          final updatedUser = UserModel_PUPPII(
                            id_PUPPII: user.id_PUPPII,
                            nickname_PUPPII: value,
                            avatarPath_PUPPII: user.avatarPath_PUPPII,
                            coinBalance_PUPPII: user.coinBalance_PUPPII,
                            favoriteCharacterIds_PUPPII:
                                user.favoriteCharacterIds_PUPPII,
                          );
                          await _userService_PUPPII
                              .updateUser_PUPPII(updatedUser);
                        }
                        if (mounted) {
                          Navigator.of(context).pop();
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(8.r),
                          color: AppTheme_PUPPII.primaryColor_PUPPII
                              .withOpacity(0.1),
                        ),
                        child: Text(
                          'Save',
                          textAlign: TextAlign.center,
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                            color: AppTheme_PUPPII.primaryColor_PUPPII,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAboutDialog_PUPPII() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // App Logo
              Container(
                width: 80.w,
                height: 80.w,
                decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20.r),
                  child: Image.asset(
                    'assets_puppii/icons_puppii/logo.png',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Icon(
                          Icons.apps,
                          size: 40.sp,
                          color: AppTheme_PUPPII.primaryColor_PUPPII,
                        ),
                      );
                    },
                  ),
                ),
              ),

              SizedBox(height: 16.h),

              // App Name
              Text(
                'Puppii',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme_PUPPII.primaryColor_PUPPII,
                ),
              ),

              SizedBox(height: 8.h),

              // App Description
              Text(
                'AI Woven Coaster Designers',
                style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                  fontSize: 16.sp,
                  color: AppTheme_PUPPII.textSecondary_PUPPII,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 16.h),

              // Version
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  'Version 1.0.0',
                  style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                    fontSize: 14.sp,
                    color: AppTheme_PUPPII.textSecondary_PUPPII,
                  ),
                ),
              ),

              SizedBox(height: 24.h),

              // Protocol Links
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const WebViewPage_PUPPII_wxyz(
                              url:
                                  'https://docs.google.com/document/d/1AWNsJmHe2PqjPt2qQnL2lkvW8OYz_7Vv5Dyqohp71W4/edit?usp=sharing',
                              title: 'Privacy Policy',
                            ),
                          ),
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'Privacy Policy',
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                            color: AppTheme_PUPPII.primaryColor_PUPPII,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const WebViewPage_PUPPII_wxyz(
                              url:
                                  'https://docs.google.com/document/d/1RNoMB51ReDSWP9vVK_HqZ6uwVSh00CFA4eEucvzCrok/edit?usp=sharing',
                              title: 'Terms of Service',
                            ),
                          ),
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        decoration:
                            AppTheme_PUPPII.neumorphismCard_PUPPII.copyWith(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'Terms of Service',
                          style: AppTheme_PUPPII.bodyText_PUPPII.copyWith(
                            fontSize: 14.sp,
                            color: AppTheme_PUPPII.primaryColor_PUPPII,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ).then((_) {
      // 确保弹窗关闭时不让任何输入框获取焦点
      if (mounted) {
        FocusScope.of(context).unfocus();
      }
    });
  }
}
