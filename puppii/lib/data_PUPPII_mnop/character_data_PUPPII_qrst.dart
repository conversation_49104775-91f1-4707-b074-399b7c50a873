import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';

class CharacterData_PUPPII {
  static const int _defaultFreeChatCount_PUPPII = 3;

  // 十个AI角色的mock数据
  static final List<CharacterModel_PUPPII> characters_PUPPII = [
    // 1. 几何图案编织师
    CharacterModel_PUPPII(
      id_PUPPII: 'geo_weave_001',
      nickname_PUPPII: 'GeoWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/1.png',
      shortIntroduction_PUPPII: 'Craft geometric-shaped coasters.',
      longIntroduction_PUPPII:
          'Design square, hexagonal, or triangular coasters using basic stitches (chain, single crochet). Add color-blocked edges or layered angles, like a starburst pattern with contrasting yarns, for modern appeal.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 2. 自然元素造型师
    CharacterModel_PUPPII(
      id_PUPPII: 'nature_weave_002',
      nickname_PUPPII: 'NatureWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/2.png',
      shortIntroduction_PUPPII: 'Weave nature-inspired coasters.',
      longIntroduction_PUPPII:
          'Create leaf, flower, or tree-shaped coasters with textured stitches (puff stitches for petals, moss stitch for foliage). Use earthy tones (greens, browns) or vibrant hues to mimic blooming gardens.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 3. 复古花纹重构师
    CharacterModel_PUPPII(
      id_PUPPII: 'retro_pattern_003',
      nickname_PUPPII: 'RetroPattern',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/3.png',
      shortIntroduction_PUPPII: 'Update vintage patterns for coasters.',
      longIntroduction_PUPPII:
          'Reimagine 70s macramé knots, 50s polka dots, or 30s art deco lines in mini sizes. Add modern twists, like neon yarns in retro motifs, for a mix of old and new.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 4. 立体浮雕编织师
    CharacterModel_PUPPII(
      id_PUPPII: 'relief_weave_004',
      nickname_PUPPII: 'ReliefWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/4.png',
      shortIntroduction_PUPPII: 'Make 3D textured coasters.',
      longIntroduction_PUPPII:
          'Use raised stitches (cluster stitches, popcorn stitches) to create embossed designs—waves, bubbles, or geometric bumps. Contrast with flat stitches to make patterns pop, adding grip to prevent slipping.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 5. 季节主题造型师
    CharacterModel_PUPPII(
      id_PUPPII: 'seasonal_weave_005',
      nickname_PUPPII: 'SeasonalWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/5.png',
      shortIntroduction_PUPPII: 'Design coasters for each season.',
      longIntroduction_PUPPII:
          'Winter: snowflake motifs with white/blue yarn. Spring: pastel flower buds. Summer: sun or seashell shapes. Fall: acorn or pumpkin patterns, using warm oranges and browns.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 6. 迷你故事编织师
    CharacterModel_PUPPII(
      id_PUPPII: 'story_weave_006',
      nickname_PUPPII: 'StoryWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/6.png',
      shortIntroduction_PUPPII: 'Tell tiny tales through coaster designs.',
      longIntroduction_PUPPII:
          'Craft scenes in mini—like a campfire with marshmallows, a tiny garden, or a starry night—using color changes and stitch details. Each coaster becomes a small story piece.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 7. 实用防滑造型师
    CharacterModel_PUPPII(
      id_PUPPII: 'grip_weave_007',
      nickname_PUPPII: 'GripWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/7.png',
      shortIntroduction_PUPPII:
          'Blend function and style with anti-slip design.',
      longIntroduction_PUPPII:
          'Add rubberized yarn accents, tight stitch patterns, or textured edges to prevent cups sliding. Shape as rounded squares or irregular circles for stability, without sacrificing looks.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 8. 拼接组合造型师
    CharacterModel_PUPPII(
      id_PUPPII: 'patchwork_coaster_008',
      nickname_PUPPII: 'PatchworkCoaster',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/8.png',
      shortIntroduction_PUPPII: 'Make mix-and-match coaster sets.',
      longIntroduction_PUPPII:
          'Create 4-6 coasters that connect as a larger pattern (like a puzzle) or complement each other with coordinating colors/stitches. Use scrap yarns for eco-friendly, unique sets.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 9. 极简线条编织师
    CharacterModel_PUPPII(
      id_PUPPII: 'minimal_weave_009',
      nickname_PUPPII: 'MinimalWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/9.png',
      shortIntroduction_PUPPII: 'Design sleek, simple coasters.',
      longIntroduction_PUPPII:
          'Focus on clean lines—solid colors with a single stripe, tiny geometric borders, or plain rounds with subtle texture. Perfect for modern decor, using neutral or monochromatic yarns.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),

    // 10. 互动趣味造型师
    CharacterModel_PUPPII(
      id_PUPPII: 'fun_weave_010',
      nickname_PUPPII: 'FunWeave',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/10.png',
      shortIntroduction_PUPPII: 'Add playful elements to coasters.',
      longIntroduction_PUPPII:
          'Make reversible designs (flip for a new pattern), coasters that stack into a shape (like a cube), or ones with tiny tassels/fringes. Great for kids\' tables or casual spaces.',
      freeChatCount_PUPPII: _defaultFreeChatCount_PUPPII,
    ),
  ];

  // 根据ID获取角色
  static CharacterModel_PUPPII? getCharacterById_PUPPII(String id) {
    try {
      return characters_PUPPII
          .firstWhere((character) => character.id_PUPPII == id);
    } catch (e) {
      return null;
    }
  }

  // 获取所有角色
  static List<CharacterModel_PUPPII> getAllCharacters_PUPPII() {
    return List.from(characters_PUPPII);
  }

  // 获取角色数量
  static int getCharacterCount_PUPPII() {
    return characters_PUPPII.length;
  }
}
