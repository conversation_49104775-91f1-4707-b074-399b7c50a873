import '../models_PUPPII_efgh/store_item_model_PUPPII_qrst.dart';

class StoreData_PUPPII {
  // 商店商品列表
  static List<StoreItemModel_PUPPII> getStoreItems_PUPPII() {
    return [
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400500",
        coinAmount_PUPPII: 100,
        price_PUPPII: "0.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400501",
        coinAmount_PUPPII: 500,
        price_PUPPII: "4.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400502",
        coinAmount_PUPPII: 600,
        price_PUPPII: "5.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400503",
        coinAmount_PUPPII: 1200,
        price_PUPPII: "9.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400504",
        coinAmount_PUPPII: 1560,
        price_PUPPII: "12.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400505",
        coinAmount_PUPPII: 2500,
        price_PUPPII: "19.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400506",
        coinAmount_PUPPII: 7000,
        price_PUPPII: "49.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400507",
        coinAmount_PUPPII: 8400,
        price_PUPPII: "59.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400508",
        coinAmount_PUPPII: 15000,
        price_PUPPII: "99.99",
        tag_PUPPII: "",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400509",
        coinAmount_PUPPII: 500,
        price_PUPPII: "1.99",
        tag_PUPPII: "big bag",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400510",
        coinAmount_PUPPII: 1200,
        price_PUPPII: "4.99",
        tag_PUPPII: "big bag",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400511",
        coinAmount_PUPPII: 2500,
        price_PUPPII: "11.99",
        tag_PUPPII: "big bag",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400512",
        coinAmount_PUPPII: 2600,
        price_PUPPII: "12.99",
        tag_PUPPII: "big bag",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400513",
        coinAmount_PUPPII: 7000,
        price_PUPPII: "34.99",
        tag_PUPPII: "big bag",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400514",
        coinAmount_PUPPII: 15000,
        price_PUPPII: "79.99",
        tag_PUPPII: "big bag",
      ),
      const StoreItemModel_PUPPII(
        productId_PUPPII: "400515",
        coinAmount_PUPPII: 18000,
        price_PUPPII: "99.99",
        tag_PUPPII: "big bag",
      ),
    ];
  }

  // 根据产品ID获取商品
  static StoreItemModel_PUPPII? getItemByProductId_PUPPII(String productId) {
    try {
      return getStoreItems_PUPPII().firstWhere(
        (item) => item.productId_PUPPII == productId,
      );
    } catch (e) {
      return null;
    }
  }

  // 获取所有产品ID列表
  static List<String> getAllProductIds_PUPPII() {
    return getStoreItems_PUPPII().map((item) => item.productId_PUPPII).toList();
  }

  // 获取有促销标签的商品
  static List<StoreItemModel_PUPPII> getPromotionalItems_PUPPII() {
    return getStoreItems_PUPPII().where((item) => item.hasTag_PUPPII).toList();
  }

  // 获取普通商品（无促销标签）
  static List<StoreItemModel_PUPPII> getRegularItems_PUPPII() {
    return getStoreItems_PUPPII().where((item) => !item.hasTag_PUPPII).toList();
  }

  // 根据价格排序商品
  static List<StoreItemModel_PUPPII> getItemsSortedByPrice_PUPPII({
    bool ascending = true,
  }) {
    final items = List<StoreItemModel_PUPPII>.from(getStoreItems_PUPPII());
    items.sort((a, b) {
      final priceA = double.parse(a.price_PUPPII);
      final priceB = double.parse(b.price_PUPPII);
      return ascending ? priceA.compareTo(priceB) : priceB.compareTo(priceA);
    });
    return items;
  }

  // 根据金币数量排序商品
  static List<StoreItemModel_PUPPII> getItemsSortedByCoins_PUPPII({
    bool ascending = true,
  }) {
    final items = List<StoreItemModel_PUPPII>.from(getStoreItems_PUPPII());
    items.sort((a, b) {
      return ascending
          ? a.coinAmount_PUPPII.compareTo(b.coinAmount_PUPPII)
          : b.coinAmount_PUPPII.compareTo(a.coinAmount_PUPPII);
    });
    return items;
  }

  // 获取推荐商品（基于金币数量的性价比）
  static List<StoreItemModel_PUPPII> getRecommendedItems_PUPPII() {
    final items = List<StoreItemModel_PUPPII>.from(getStoreItems_PUPPII());
    // 按照金币/价格比例排序，性价比高的在前
    items.sort((a, b) {
      final ratioA = a.coinAmount_PUPPII / double.parse(a.price_PUPPII);
      final ratioB = b.coinAmount_PUPPII / double.parse(b.price_PUPPII);
      return ratioB.compareTo(ratioA);
    });
    return items;
  }

  // 验证商品数据
  static bool isValidStoreItem_PUPPII(StoreItemModel_PUPPII item) {
    // 检查产品ID是否有效
    if (item.productId_PUPPII.isEmpty) return false;

    // 检查金币数量是否有效
    if (item.coinAmount_PUPPII <= 0) return false;

    // 检查价格是否有效
    try {
      final price = double.parse(item.price_PUPPII);
      if (price <= 0) return false;
    } catch (e) {
      return false;
    }

    return true;
  }

  // 获取商品统计信息
  static Map<String, dynamic> getStoreStats_PUPPII() {
    final items = getStoreItems_PUPPII();
    final prices =
        items.map((item) => double.parse(item.price_PUPPII)).toList();
    final coins = items.map((item) => item.coinAmount_PUPPII).toList();

    return {
      'totalItems': items.length,
      'promotionalItems': getPromotionalItems_PUPPII().length,
      'minPrice': prices.reduce((a, b) => a < b ? a : b),
      'maxPrice': prices.reduce((a, b) => a > b ? a : b),
      'minCoins': coins.reduce((a, b) => a < b ? a : b),
      'maxCoins': coins.reduce((a, b) => a > b ? a : b),
      'averagePrice': prices.reduce((a, b) => a + b) / prices.length,
      'averageCoins': coins.reduce((a, b) => a + b) / coins.length,
    };
  }
}
