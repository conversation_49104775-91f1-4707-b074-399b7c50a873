import '../models_PUPPII_efgh/user_model_PUPPII_wxyz.dart';

class UserData_PUPPII {
  // 默认用户数据
  static UserModel_PUPPII getDefaultUser_PUPPII() {
    return const UserModel_PUPPII(
      id_PUPPII: 'user_default_001_PUPPII',
      nickname_PUPPII: '<PERSON>',
      avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
      coinBalance_PUPPII: 100,
      favoriteCharacterIds_PUPPII: <String>{},
    );
  }

  // 生成随机用户ID
  static String generateUserId_PUPPII() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'user_${timestamp}_PUPPII';
  }

  // 创建新用户
  static UserModel_PUPPII createNewUser_PUPPII({
    String? nickname,
    String? avatarPath,
    int? initialCoins,
  }) {
    return UserModel_PUPPII(
      id_PUPPII: generateUserId_PUPPII(),
      nickname_PUPPII: nickname ?? '<PERSON>',
      avatarPath_PUPPII:
          avatarPath ?? 'assets_puppii/images_puppii/user_01.png',
      coinBalance_PUPPII: initialCoins ?? 100,
      favoriteCharacterIds_PUPPII: const <String>{},
    );
  }

  // 预设的用户昵称选项
  static List<String> getSuggestedNicknames_PUPPII() {
    return [
      'Alex Chen',
    ];
  }

  // 预设的用户头像路径选项
  static List<String> getAvailableAvatars_PUPPII() {
    return [
      'assets_puppii/images_puppii/user_01.png',
      'assets_puppii/images_puppii/user_02.png',
      'assets_puppii/images_puppii/user_03.png',
      'assets_puppii/images_puppii/user_04.png',
      'assets_puppii/images_puppii/user_05.png',
    ];
  }

  // 金币相关常量
  static const int dailyLoginBonus_PUPPII = 10;
  static const int chatCostPerMessage_PUPPII = 5;
  static const int premiumChatCostPerMessage_PUPPII = 10;
  static const int maxCoinBalance_PUPPII = 9999;
  static const int minCoinBalance_PUPPII = 0;

  // 收藏相关常量
  static const int maxFavoriteCharacters_PUPPII = 20;

  // 验证用户数据
  static bool isValidUserData_PUPPII(UserModel_PUPPII user) {
    // 检查ID是否有效
    if (user.id_PUPPII.isEmpty) return false;

    // 检查昵称是否有效
    if (user.nickname_PUPPII.isEmpty || user.nickname_PUPPII.length > 20)
      return false;

    // 检查头像路径是否有效
    if (user.avatarPath_PUPPII.isEmpty) return false;

    // 检查金币余额是否在有效范围内
    if (user.coinBalance_PUPPII < minCoinBalance_PUPPII ||
        user.coinBalance_PUPPII > maxCoinBalance_PUPPII) return false;

    // 检查收藏角色数量是否超限
    if (user.favoriteCharacterIds_PUPPII.length > maxFavoriteCharacters_PUPPII)
      return false;

    return true;
  }

  // 获取用户等级（基于金币余额）
  static int getUserLevel_PUPPII(int coinBalance) {
    if (coinBalance >= 1000) return 5; // 钻石
    if (coinBalance >= 500) return 4; // 黄金
    if (coinBalance >= 200) return 3; // 白银
    if (coinBalance >= 50) return 2; // 青铜
    return 1; // 新手
  }

  // 获取用户等级名称
  static String getUserLevelName_PUPPII(int level) {
    switch (level) {
      case 5:
        return 'Diamond';
      case 4:
        return 'Gold';
      case 3:
        return 'Silver';
      case 2:
        return 'Bronze';
      case 1:
        return 'Rookie';
      default:
        return 'Unknown';
    }
  }

  // 获取用户统计信息
  static Map<String, dynamic> getUserStats_PUPPII(UserModel_PUPPII user) {
    final level = getUserLevel_PUPPII(user.coinBalance_PUPPII);
    return {
      'level': level,
      'levelName': getUserLevelName_PUPPII(level),
      'coinBalance': user.coinBalance_PUPPII,
      'favoriteCount': user.favoriteCharacterIds_PUPPII.length,
      'canAddMoreFavorites': user.favoriteCharacterIds_PUPPII.length <
          maxFavoriteCharacters_PUPPII,
    };
  }
}
