import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'theme_PUPPII_abcd/app_theme_PUPPII.dart';
import 'views_PUPPII_mnop/main_navigation_PUPPII_ghij.dart';

void main() {
  runApp(const PuppiiApp());
}

class PuppiiApp extends StatelessWidget {
  const PuppiiApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          title: 'Puppii - AI Woven Coaster Designers',
          theme: AppTheme_PUPPII.lightTheme_PUPPII,
          debugShowCheckedModeBanner: false,
          home: const MainNavigation_PUPPII(),
        );
      },
    );
  }
}
