import 'package:dio/dio.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';

class DoubaoApiService_PUPPII {
  static const String _baseUrl_PUPPII =
      'https://ark.cn-beijing.volces.com/api/v3';
  static const String _apiKey_PUPPII =
      '13fad018-f931-4917-af0d-f7153d1530ef'; // 需要替换为真实的API Key
  static const String _model_PUPPII =
      'doubao-1-5-thinking-pro-250415'; // 豆包模型名称

  late final Dio _dio_PUPPII;

  DoubaoApiService_PUPPII() {
    _dio_PUPPII = Dio(BaseOptions(
      baseUrl: _baseUrl_PUPPII,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey_PUPPII',
        'Accept': 'application/json',
      },
    ));

    // 添加拦截器用于日志
    _dio_PUPPII.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: true,
      logPrint: (obj) => print('[DoubaoAPI] $obj'),
    ));
  }

  // 发送聊天消息
  Future<String> sendMessage_PUPPII({
    required String userMessage,
    required CharacterModel_PUPPII character,
    List<Map<String, String>>? conversationHistory,
  }) async {
    try {
      // 首先尝试默认模型
      final systemPrompt = _buildSystemPrompt_PUPPII(character);
      final messages = <Map<String, String>>[
        {'role': 'system', 'content': systemPrompt},
        ...?conversationHistory,
        {'role': 'user', 'content': userMessage},
      ];

      final response = await _dio_PUPPII.post(
        '/chat/completions',
        data: {
          'model': _model_PUPPII,
          'messages': messages,
          'max_tokens': 1000,
          'temperature': 0.7,
          'stream': false,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final content = data['choices'][0]['message']['content'] as String;
        // 清理无效的UTF-16字符
        final cleanContent = _cleanUtf16String_PUPPII(content.trim());
        return cleanContent;
      } else {
        throw Exception(
            'API request failed with status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('[DoubaoAPI] Default model failed, trying alternatives...');

      // 如果默认模型失败，尝试其他模型
      try {
        return await _tryDifferentModels_PUPPII(
          userMessage: userMessage,
          character: character,
          conversationHistory: conversationHistory,
        );
      } catch (alternativeError) {
        print('[DoubaoAPI] All models failed');
        throw Exception('Network error: ${e.message}');
      }
    } catch (e) {
      print('[DoubaoAPI] Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  // 清理无效的UTF-16字符
  String _cleanUtf16String_PUPPII(String input) {
    if (input.isEmpty) return input;

    try {
      // 使用更严格的方法清理字符串
      final buffer = StringBuffer();

      for (int i = 0; i < input.length; i++) {
        final char = input[i];
        final codeUnit = char.codeUnitAt(0);

        // 基本ASCII字符 (包括空格、标点符号等)
        if (codeUnit >= 0x0020 && codeUnit <= 0x007E) {
          buffer.write(char);
        }
        // 常见的空白字符
        else if (codeUnit == 0x0009 ||
            codeUnit == 0x000A ||
            codeUnit == 0x000D) {
          buffer.write(char);
        }
        // 常见的Unicode字符 (但排除emoji和特殊符号)
        else if (codeUnit >= 0x00A0 && codeUnit <= 0x00FF) {
          // 拉丁字符补充
          buffer.write(char);
        } else if (codeUnit >= 0x2000 && codeUnit <= 0x206F) {
          // 一般标点符号，但排除一些特殊字符
          if (codeUnit == 0x2013 ||
              codeUnit == 0x2014 || // 短横线、长横线
              codeUnit == 0x2018 ||
              codeUnit == 0x2019 || // 单引号
              codeUnit == 0x201C ||
              codeUnit == 0x201D) {
            // 双引号
            buffer.write(char);
          }
        }
        // 跳过emoji和其他可能有问题的字符
      }

      final result = buffer.toString();

      // 验证结果字符串是否有效
      try {
        // 尝试创建一个TextSpan来验证字符串是否有效
        result.runes.toList();
        return result;
      } catch (e) {
        print('[DoubaoAPI] Validation failed, using fallback cleaning');
        // 如果验证失败，使用更保守的方法
        return input.replaceAll(RegExp(r'[^\x20-\x7E\n\r\t]'), '');
      }
    } catch (e) {
      print('[DoubaoAPI] Error cleaning UTF-16 string: $e');
      // 如果清理失败，返回只包含基本ASCII字符的版本
      return input.replaceAll(RegExp(r'[^\x20-\x7E\n\r\t]'), '');
    }
  }

  // 构建系统提示词
  String _buildSystemPrompt_PUPPII(CharacterModel_PUPPII character) {
    return '''
You are ${character.nickname_PUPPII}, a professional AI coaster weaving designer assistant.

Character Description:
- Short Introduction: ${character.shortIntroduction_PUPPII}
- Detailed Introduction: ${character.longIntroduction_PUPPII}

Instructions:
1. Always respond in English only
2. Stay in character as ${character.nickname_PUPPII}
3. Focus on coaster weaving, design, and crafting topics
4. Be helpful, creative, and inspiring
5. Provide practical advice and suggestions
6. Keep responses conversational and engaging
7. If asked about non-weaving topics, gently redirect to your expertise area

Remember: You are an expert in coaster weaving and design. Help users create beautiful, functional coasters while maintaining your character personality.
''';
  }

  // 测试不同的模型名称
  Future<String> _tryDifferentModels_PUPPII({
    required String userMessage,
    required CharacterModel_PUPPII character,
    List<Map<String, String>>? conversationHistory,
  }) async {
    final modelNames = [
      'doubao-1-5-thinking-pro-250415',
    ];

    for (final modelName in modelNames) {
      try {
        print('[DoubaoAPI] Trying model: $modelName');

        final systemPrompt = _buildSystemPrompt_PUPPII(character);
        final messages = <Map<String, String>>[
          {'role': 'system', 'content': systemPrompt},
          ...?conversationHistory,
          {'role': 'user', 'content': userMessage},
        ];

        final response = await _dio_PUPPII.post(
          '/chat/completions',
          data: {
            'model': modelName,
            'messages': messages,
            'max_tokens': 1000,
            'temperature': 0.7,
            'stream': false,
          },
        );

        if (response.statusCode == 200) {
          final data = response.data;
          final content = data['choices'][0]['message']['content'] as String;
          print('[DoubaoAPI] Success with model: $modelName');
          // 清理无效的UTF-16字符
          final cleanContent = _cleanUtf16String_PUPPII(content.trim());
          return cleanContent;
        }
      } catch (e) {
        print('[DoubaoAPI] Model $modelName failed: $e');
        continue;
      }
    }

    throw Exception('All model attempts failed');
  }

  // 简单的API测试
  Future<void> testApiDirectly_PUPPII() async {
    try {
      print('[DoubaoAPI] Testing API with endpoint: $_model_PUPPII');
      print('[DoubaoAPI] Base URL: $_baseUrl_PUPPII');
      print('[DoubaoAPI] API Key: ${_apiKey_PUPPII.substring(0, 8)}...');

      final response = await _dio_PUPPII.post(
        '/chat/completions',
        data: {
          'model': _model_PUPPII,
          'messages': [
            {'role': 'user', 'content': 'Hello'}
          ],
          'max_tokens': 100,
          'temperature': 0.7,
        },
      );

      print('[DoubaoAPI] Test successful: ${response.statusCode}');
    } catch (e) {
      print('[DoubaoAPI] Test failed: $e');
    }
  }

  // 测试API连接
  Future<bool> testConnection_PUPPII() async {
    try {
      final response = await sendMessage_PUPPII(
        userMessage: 'Hello, can you help me with coaster design?',
        character: CharacterModel_PUPPII(
          id_PUPPII: 'test',
          nickname_PUPPII: 'TestWeaver',
          avatarPath_PUPPII: '',
          shortIntroduction_PUPPII: 'Test weaver',
          longIntroduction_PUPPII: 'A test weaving assistant',
          freeChatCount_PUPPII: 3,
        ),
      );
      return response.isNotEmpty;
    } catch (e) {
      print('[DoubaoAPI] Connection test failed: $e');
      return false;
    }
  }
}
