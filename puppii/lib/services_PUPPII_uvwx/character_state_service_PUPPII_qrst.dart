import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models_PUPPII_efgh/character_model_PUPPII_ijkl.dart';
import '../data_PUPPII_mnop/character_data_PUPPII_qrst.dart';

class CharacterStateService_PUPPII extends GetxController {
  static CharacterStateService_PUPPII get instance => Get.find();

  // 角色列表的响应式状态
  final RxList<CharacterModel_PUPPII> characters_PUPPII =
      <CharacterModel_PUPPII>[].obs;

  // 是否正在加载
  final RxBool isLoading_PUPPII = false.obs;

  // 历史页面更新触发器
  final RxBool historyUpdateTrigger_PUPPII = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeCharacters_PUPPII();
  }

  // 初始化角色数据
  Future<void> _initializeCharacters_PUPPII() async {
    isLoading_PUPPII.value = true;

    try {
      // 获取基础角色数据
      final baseCharacters = CharacterData_PUPPII.getAllCharacters_PUPPII();

      // 从本地存储加载每个角色的实际免费次数
      final updatedCharacters = <CharacterModel_PUPPII>[];

      for (final character in baseCharacters) {
        final actualFreeCount =
            await _getCharacterFreeCount_PUPPII(character.id_PUPPII);

        // 创建更新后的角色对象
        final updatedCharacter = CharacterModel_PUPPII(
          id_PUPPII: character.id_PUPPII,
          nickname_PUPPII: character.nickname_PUPPII,
          avatarPath_PUPPII: character.avatarPath_PUPPII,
          shortIntroduction_PUPPII: character.shortIntroduction_PUPPII,
          longIntroduction_PUPPII: character.longIntroduction_PUPPII,
          freeChatCount_PUPPII: actualFreeCount,
        );

        updatedCharacters.add(updatedCharacter);
      }

      characters_PUPPII.value = updatedCharacters;
    } catch (e) {
      print('[CharacterStateService] Error initializing characters: $e');
      // 如果出错，使用默认数据
      characters_PUPPII.value = CharacterData_PUPPII.getAllCharacters_PUPPII();
    } finally {
      isLoading_PUPPII.value = false;
    }
  }

  // 获取角色的实际免费次数
  Future<int> _getCharacterFreeCount_PUPPII(String characterId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'character_free_count_${characterId}';

      // 如果没有存储过，返回默认值
      if (!prefs.containsKey(key)) {
        const defaultCount = 3;
        await prefs.setInt(key, defaultCount);
        return defaultCount;
      }

      return prefs.getInt(key) ?? 3;
    } catch (e) {
      print(
          '[CharacterStateService] Error getting free count for $characterId: $e');
      return 3; // 默认值
    }
  }

  // 更新角色的免费次数
  Future<void> updateCharacterFreeCount_PUPPII(
      String characterId, int newCount) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'character_free_count_${characterId}';

      // 保存到本地存储
      await prefs.setInt(key, newCount);

      // 更新内存中的数据
      final characterIndex = characters_PUPPII.indexWhere(
        (character) => character.id_PUPPII == characterId,
      );

      if (characterIndex != -1) {
        final oldCharacter = characters_PUPPII[characterIndex];
        final updatedCharacter = CharacterModel_PUPPII(
          id_PUPPII: oldCharacter.id_PUPPII,
          nickname_PUPPII: oldCharacter.nickname_PUPPII,
          avatarPath_PUPPII: oldCharacter.avatarPath_PUPPII,
          shortIntroduction_PUPPII: oldCharacter.shortIntroduction_PUPPII,
          longIntroduction_PUPPII: oldCharacter.longIntroduction_PUPPII,
          freeChatCount_PUPPII: newCount,
        );

        characters_PUPPII[characterIndex] = updatedCharacter;
      }

      print(
          '[CharacterStateService] Updated free count for $characterId: $newCount');
    } catch (e) {
      print(
          '[CharacterStateService] Error updating free count for $characterId: $e');
    }
  }

  // 减少角色的免费次数（发送消息时调用）
  Future<void> decrementCharacterFreeCount_PUPPII(String characterId) async {
    final character = characters_PUPPII.firstWhereOrNull(
      (char) => char.id_PUPPII == characterId,
    );

    if (character != null && character.freeChatCount_PUPPII > 0) {
      final newCount = character.freeChatCount_PUPPII - 1;
      await updateCharacterFreeCount_PUPPII(characterId, newCount);
    }
  }

  // 获取特定角色
  CharacterModel_PUPPII? getCharacterById_PUPPII(String characterId) {
    return characters_PUPPII.firstWhereOrNull(
      (character) => character.id_PUPPII == characterId,
    );
  }

  // 刷新所有角色数据
  Future<void> refreshCharacters_PUPPII() async {
    await _initializeCharacters_PUPPII();
  }

  // 重置角色的免费次数（用于测试或管理）
  Future<void> resetCharacterFreeCount_PUPPII(String characterId) async {
    await updateCharacterFreeCount_PUPPII(characterId, 3);
  }

  // 重置所有角色的免费次数
  Future<void> resetAllCharacterFreeCounts_PUPPII() async {
    for (final character in characters_PUPPII) {
      await resetCharacterFreeCount_PUPPII(character.id_PUPPII);
    }
  }

  // 通知历史页面更新
  void notifyHistoryUpdate_PUPPII() {
    historyUpdateTrigger_PUPPII.value = !historyUpdateTrigger_PUPPII.value;
  }
}
