import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models_PUPPII_efgh/chat_session_PUPPII_qrst.dart';
import '../models_PUPPII_efgh/chat_message_PUPPII_mnop.dart';

class ChatStorageService_PUPPII {
  static const String _sessionsKey_PUPPII = 'chat_sessions_puppii';
  static const String _userDataKey_PUPPII = 'user_data_puppii';

  // 保存聊天会话
  static Future<void> saveChatSession_PUPPII(ChatSession_PUPPII session) async {
    final prefs = await SharedPreferences.getInstance();
    final sessions = await getAllChatSessions_PUPPII();
    
    // 更新或添加会话
    final existingIndex = sessions.indexWhere((s) => s.sessionId_PUPPII == session.sessionId_PUPPII);
    if (existingIndex != -1) {
      sessions[existingIndex] = session;
    } else {
      sessions.add(session);
    }

    // 保存到本地存储
    final sessionsJson = sessions.map((s) => s.toJson()).toList();
    await prefs.setString(_sessionsKey_PUPPII, jsonEncode(sessionsJson));
  }

  // 获取所有聊天会话
  static Future<List<ChatSession_PUPPII>> getAllChatSessions_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsString = prefs.getString(_sessionsKey_PUPPII);
    
    if (sessionsString == null) return [];

    try {
      final sessionsList = jsonDecode(sessionsString) as List;
      return sessionsList
          .map((json) => ChatSession_PUPPII.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('[ChatStorage] Error loading sessions: $e');
      return [];
    }
  }

  // 根据角色ID获取聊天会话
  static Future<ChatSession_PUPPII?> getChatSessionByCharacter_PUPPII(String characterId) async {
    final sessions = await getAllChatSessions_PUPPII();
    try {
      return sessions.firstWhere((s) => s.character_PUPPII.id_PUPPII == characterId);
    } catch (e) {
      return null;
    }
  }

  // 添加消息到会话
  static Future<void> addMessageToSession_PUPPII(
    String sessionId,
    ChatMessage_PUPPII message,
  ) async {
    final sessions = await getAllChatSessions_PUPPII();
    final sessionIndex = sessions.indexWhere((s) => s.sessionId_PUPPII == sessionId);
    
    if (sessionIndex != -1) {
      final updatedMessages = List<ChatMessage_PUPPII>.from(sessions[sessionIndex].messages_PUPPII);
      updatedMessages.add(message);
      
      final updatedSession = sessions[sessionIndex].copyWith(
        messages_PUPPII: updatedMessages,
        lastUpdated_PUPPII: DateTime.now(),
      );
      
      await saveChatSession_PUPPII(updatedSession);
    }
  }

  // 更新消息状态
  static Future<void> updateMessageStatus_PUPPII(
    String sessionId,
    String messageId,
    MessageStatus_PUPPII status,
    {String? content}
  ) async {
    final sessions = await getAllChatSessions_PUPPII();
    final sessionIndex = sessions.indexWhere((s) => s.sessionId_PUPPII == sessionId);
    
    if (sessionIndex != -1) {
      final messages = List<ChatMessage_PUPPII>.from(sessions[sessionIndex].messages_PUPPII);
      final messageIndex = messages.indexWhere((m) => m.id_PUPPII == messageId);
      
      if (messageIndex != -1) {
        messages[messageIndex] = messages[messageIndex].copyWith(
          status_PUPPII: status,
          content_PUPPII: content ?? messages[messageIndex].content_PUPPII,
        );
        
        final updatedSession = sessions[sessionIndex].copyWith(
          messages_PUPPII: messages,
          lastUpdated_PUPPII: DateTime.now(),
        );
        
        await saveChatSession_PUPPII(updatedSession);
      }
    }
  }

  // 更新会话收藏状态
  static Future<void> updateSessionFavorite_PUPPII(String sessionId, bool isFavorite) async {
    final sessions = await getAllChatSessions_PUPPII();
    final sessionIndex = sessions.indexWhere((s) => s.sessionId_PUPPII == sessionId);
    
    if (sessionIndex != -1) {
      final updatedSession = sessions[sessionIndex].copyWith(
        isFavorite_PUPPII: isFavorite,
        lastUpdated_PUPPII: DateTime.now(),
      );
      
      await saveChatSession_PUPPII(updatedSession);
    }
  }

  // 更新剩余聊天次数
  static Future<void> updateRemainingChats_PUPPII(String sessionId, int remainingChats) async {
    final sessions = await getAllChatSessions_PUPPII();
    final sessionIndex = sessions.indexWhere((s) => s.sessionId_PUPPII == sessionId);
    
    if (sessionIndex != -1) {
      final updatedSession = sessions[sessionIndex].copyWith(
        remainingChats_PUPPII: remainingChats,
        lastUpdated_PUPPII: DateTime.now(),
      );
      
      await saveChatSession_PUPPII(updatedSession);
    }
  }

  // 删除聊天会话
  static Future<void> deleteChatSession_PUPPII(String sessionId) async {
    final sessions = await getAllChatSessions_PUPPII();
    sessions.removeWhere((s) => s.sessionId_PUPPII == sessionId);
    
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = sessions.map((s) => s.toJson()).toList();
    await prefs.setString(_sessionsKey_PUPPII, jsonEncode(sessionsJson));
  }

  // 清除所有聊天数据
  static Future<void> clearAllChatData_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_sessionsKey_PUPPII);
  }

  // 保存用户数据
  static Future<void> saveUserData_PUPPII(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userDataKey_PUPPII, jsonEncode(userData));
  }

  // 获取用户数据
  static Future<Map<String, dynamic>> getUserData_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(_userDataKey_PUPPII);
    
    if (userDataString == null) {
      // 返回默认用户数据
      return {
        'nickname': 'Puppii User',
        'avatar': 'assets_puppii/images_puppii/user_avatar.png',
      };
    }

    try {
      return jsonDecode(userDataString) as Map<String, dynamic>;
    } catch (e) {
      print('[ChatStorage] Error loading user data: $e');
      return {
        'nickname': 'Puppii User',
        'avatar': 'assets_puppii/images_puppii/user_avatar.png',
      };
    }
  }
}
