import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';
import '../models_PUPPII_efgh/soul_question_model_PUPPII_wxyz.dart';
import 'user_service_PUPPII_mnop.dart';

class SoulQuestionService_PUPPII extends GetxController {
  static const String _keyLastAnswerDate_PUPPII =
      'soul_question_last_answer_date_PUPPII';
  static const String _keyAnswerHistory_PUPPII =
      'soul_question_answer_history_PUPPII';
  static const String _keyFirstTimeUser_PUPPII =
      'soul_question_first_time_user_PUPPII';
  static const String _keyTodayQuestionId_PUPPII =
      'soul_question_today_id_PUPPII';

  final Rx<SoulQuestionModel_PUPPII?> todayQuestion_PUPPII =
      Rx<SoulQuestionModel_PUPPII?>(null);
  final RxBool canAnswerToday_PUPPII = true.obs;
  final RxBool isFirstTimeUser_PUPPII = true.obs;
  final RxList<SoulQuestionAnswer_PUPPII> answerHistory_PUPPII =
      <SoulQuestionAnswer_PUPPII>[].obs;
  final RxBool isLoading_PUPPII = false.obs;

  UserService_PUPPII? _userService_PUPPII;

  UserService_PUPPII get userService_PUPPII {
    if (_userService_PUPPII == null) {
      try {
        _userService_PUPPII = Get.find<UserService_PUPPII>();
      } catch (e) {
        print('[SoulQuestionService] UserService not ready yet: $e');
        throw Exception('UserService not available');
      }
    }
    return _userService_PUPPII!;
  }

  @override
  void onInit() {
    super.onInit();
    _loadData_PUPPII();
  }

  // 加载数据
  Future<void> _loadData_PUPPII() async {
    isLoading_PUPPII.value = true;
    try {
      final prefs = await SharedPreferences.getInstance();

      // 检查是否首次使用
      isFirstTimeUser_PUPPII.value =
          prefs.getBool(_keyFirstTimeUser_PUPPII) ?? true;

      // 加载回答历史
      final historyJson = prefs.getString(_keyAnswerHistory_PUPPII);
      if (historyJson != null) {
        final List<dynamic> historyList = json.decode(historyJson);
        answerHistory_PUPPII.value = historyList
            .map((item) => SoulQuestionAnswer_PUPPII.fromJson_PUPPII(item))
            .toList();
      }

      // 检查今日是否可以回答
      await _checkTodayStatus_PUPPII();
    } catch (e) {
      print('[SoulQuestionService] Error loading data: $e');
    } finally {
      isLoading_PUPPII.value = false;
    }
  }

  // 检查今日状态
  Future<void> _checkTodayStatus_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    final lastAnswerDateStr = prefs.getString(_keyLastAnswerDate_PUPPII);
    final todayStr = _getTodayString_PUPPII();

    if (lastAnswerDateStr == todayStr) {
      // 今天已经回答过了
      canAnswerToday_PUPPII.value = false;
      // 尝试加载今天的问题
      final todayQuestionId = prefs.getString(_keyTodayQuestionId_PUPPII);
      if (todayQuestionId != null) {
        todayQuestion_PUPPII.value = SoulQuestionData_PUPPII.questions_PUPPII
            .firstWhereOrNull((q) => q.id_PUPPII == todayQuestionId);
      }
    } else {
      // 今天还没有回答过
      canAnswerToday_PUPPII.value = true;
      todayQuestion_PUPPII.value = null;
    }
  }

  // 获取今日问题
  Future<SoulQuestionModel_PUPPII?> getTodayQuestion_PUPPII() async {
    if (todayQuestion_PUPPII.value != null) {
      return todayQuestion_PUPPII.value;
    }

    if (!canAnswerToday_PUPPII.value) {
      return null;
    }

    // 生成今日问题
    final question = _generateTodayQuestion_PUPPII();
    todayQuestion_PUPPII.value = question;

    // 保存今日问题ID
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyTodayQuestionId_PUPPII, question.id_PUPPII);

    return question;
  }

  // 生成今日问题（基于日期的伪随机）
  SoulQuestionModel_PUPPII _generateTodayQuestion_PUPPII() {
    final today = DateTime.now();
    final seed = today.year * 10000 + today.month * 100 + today.day;
    final random = Random(seed);

    // 过滤掉最近7天回答过的问题
    final recentQuestionIds = answerHistory_PUPPII
        .where((answer) =>
            DateTime.now().difference(answer.answeredAt_PUPPII).inDays < 7)
        .map((answer) => answer.questionId_PUPPII)
        .toSet();

    final availableQuestions = SoulQuestionData_PUPPII.questions_PUPPII
        .where((q) => !recentQuestionIds.contains(q.id_PUPPII))
        .toList();

    if (availableQuestions.isEmpty) {
      // 如果所有问题都在最近7天内回答过，就从全部问题中选择
      final index =
          random.nextInt(SoulQuestionData_PUPPII.questions_PUPPII.length);
      return SoulQuestionData_PUPPII.questions_PUPPII[index];
    }

    final index = random.nextInt(availableQuestions.length);
    return availableQuestions[index];
  }

  // 提交答案
  Future<bool> submitAnswer_PUPPII(String answer) async {
    if (!canAnswerToday_PUPPII.value || todayQuestion_PUPPII.value == null) {
      return false;
    }

    if (answer.trim().isEmpty) {
      return false;
    }

    try {
      final question = todayQuestion_PUPPII.value!;
      final answerRecord = SoulQuestionAnswer_PUPPII(
        questionId_PUPPII: question.id_PUPPII,
        answer_PUPPII: answer.trim(),
        answeredAt_PUPPII: DateTime.now(),
        coinsEarned_PUPPII: question.rewardCoins_PUPPII,
      );

      // 添加到历史记录
      answerHistory_PUPPII.add(answerRecord);

      // 保存数据
      await _saveData_PUPPII();

      // 发放金币奖励
      await userService_PUPPII.addCoins_PUPPII(question.rewardCoins_PUPPII);

      // 更新状态
      canAnswerToday_PUPPII.value = false;

      // 标记不是首次用户
      if (isFirstTimeUser_PUPPII.value) {
        isFirstTimeUser_PUPPII.value = false;
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_keyFirstTimeUser_PUPPII, false);
      }

      return true;
    } catch (e) {
      print('[SoulQuestionService] Error submitting answer: $e');
      return false;
    }
  }

  // 保存数据
  Future<void> _saveData_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();

    // 保存最后回答日期
    await prefs.setString(_keyLastAnswerDate_PUPPII, _getTodayString_PUPPII());

    // 保存回答历史
    final historyJson = json.encode(
      answerHistory_PUPPII.map((answer) => answer.toJson_PUPPII()).toList(),
    );
    await prefs.setString(_keyAnswerHistory_PUPPII, historyJson);
  }

  // 获取今日日期字符串
  String _getTodayString_PUPPII() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  // 获取统计信息
  Map<String, int> getStats_PUPPII() {
    final totalAnswers = answerHistory_PUPPII.length;
    final totalCoinsEarned = answerHistory_PUPPII.fold<int>(
        0, (sum, answer) => sum + answer.coinsEarned_PUPPII);

    final thisMonthAnswers = answerHistory_PUPPII.where((answer) {
      final now = DateTime.now();
      final answerDate = answer.answeredAt_PUPPII;
      return answerDate.year == now.year && answerDate.month == now.month;
    }).length;

    return {
      'totalAnswers': totalAnswers,
      'totalCoinsEarned': totalCoinsEarned,
      'thisMonthAnswers': thisMonthAnswers,
    };
  }

  // 重置今日状态（用于测试）
  Future<void> resetTodayStatus_PUPPII() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyLastAnswerDate_PUPPII);
    await prefs.remove(_keyTodayQuestionId_PUPPII);
    canAnswerToday_PUPPII.value = true;
    todayQuestion_PUPPII.value = null;
  }
}
