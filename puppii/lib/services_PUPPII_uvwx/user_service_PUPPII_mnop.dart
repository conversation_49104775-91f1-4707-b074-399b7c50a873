import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models_PUPPII_efgh/user_model_PUPPII_wxyz.dart';
import '../data_PUPPII_mnop/user_data_PUPPII_abcd.dart';

class UserService_PUPPII extends GetxController {
  static UserService_PUPPII get instance => Get.find();
  
  // 用户数据的响应式状态
  final Rx<UserModel_PUPPII?> currentUser_PUPPII = Rx<UserModel_PUPPII?>(null);
  
  // 是否正在加载
  final RxBool isLoading_PUPPII = false.obs;
  
  // 存储键
  static const String _userDataKey_PUPPII = 'user_data_puppii_v1';

  @override
  void onInit() {
    super.onInit();
    _initializeUser_PUPPII();
  }

  // 初始化用户数据
  Future<void> _initializeUser_PUPPII() async {
    isLoading_PUPPII.value = true;
    
    try {
      final userData = await _loadUserFromStorage_PUPPII();
      if (userData != null) {
        currentUser_PUPPII.value = userData;
      } else {
        // 如果没有用户数据，创建默认用户
        final defaultUser = UserData_PUPPII.getDefaultUser_PUPPII();
        await _saveUserToStorage_PUPPII(defaultUser);
        currentUser_PUPPII.value = defaultUser;
      }
    } catch (e) {
      print('[UserService] Error initializing user: $e');
      // 出错时使用默认用户
      currentUser_PUPPII.value = UserData_PUPPII.getDefaultUser_PUPPII();
    } finally {
      isLoading_PUPPII.value = false;
    }
  }

  // 从本地存储加载用户数据
  Future<UserModel_PUPPII?> _loadUserFromStorage_PUPPII() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(_userDataKey_PUPPII);
      
      if (userDataString != null) {
        final userDataJson = jsonDecode(userDataString) as Map<String, dynamic>;
        return UserModel_PUPPII.fromJson(userDataJson);
      }
    } catch (e) {
      print('[UserService] Error loading user from storage: $e');
    }
    return null;
  }

  // 保存用户数据到本地存储
  Future<void> _saveUserToStorage_PUPPII(UserModel_PUPPII user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = jsonEncode(user.toJson());
      await prefs.setString(_userDataKey_PUPPII, userDataString);
    } catch (e) {
      print('[UserService] Error saving user to storage: $e');
    }
  }

  // 更新用户数据
  Future<void> updateUser_PUPPII(UserModel_PUPPII user) async {
    if (!UserData_PUPPII.isValidUserData_PUPPII(user)) {
      throw Exception('Invalid user data');
    }
    
    currentUser_PUPPII.value = user;
    await _saveUserToStorage_PUPPII(user);
  }

  // 更新用户昵称
  Future<void> updateNickname_PUPPII(String nickname) async {
    final user = currentUser_PUPPII.value;
    if (user != null) {
      final updatedUser = user.copyWith(nickname_PUPPII: nickname);
      await updateUser_PUPPII(updatedUser);
    }
  }

  // 更新用户头像
  Future<void> updateAvatar_PUPPII(String avatarPath) async {
    final user = currentUser_PUPPII.value;
    if (user != null) {
      final updatedUser = user.copyWith(avatarPath_PUPPII: avatarPath);
      await updateUser_PUPPII(updatedUser);
    }
  }

  // 增加金币
  Future<void> addCoins_PUPPII(int amount) async {
    final user = currentUser_PUPPII.value;
    if (user != null && amount > 0) {
      final updatedUser = user.addCoins_PUPPII(amount);
      await updateUser_PUPPII(updatedUser);
    }
  }

  // 减少金币
  Future<bool> subtractCoins_PUPPII(int amount) async {
    final user = currentUser_PUPPII.value;
    if (user != null && user.hasEnoughCoins_PUPPII(amount)) {
      final updatedUser = user.subtractCoins_PUPPII(amount);
      await updateUser_PUPPII(updatedUser);
      return true;
    }
    return false;
  }

  // 添加收藏角色
  Future<bool> addFavoriteCharacter_PUPPII(String characterId) async {
    final user = currentUser_PUPPII.value;
    if (user != null && 
        !user.isFavoriteCharacter_PUPPII(characterId) &&
        user.favoriteCharacterIds_PUPPII.length < UserData_PUPPII.maxFavoriteCharacters_PUPPII) {
      final updatedUser = user.addFavoriteCharacter_PUPPII(characterId);
      await updateUser_PUPPII(updatedUser);
      return true;
    }
    return false;
  }

  // 移除收藏角色
  Future<bool> removeFavoriteCharacter_PUPPII(String characterId) async {
    final user = currentUser_PUPPII.value;
    if (user != null && user.isFavoriteCharacter_PUPPII(characterId)) {
      final updatedUser = user.removeFavoriteCharacter_PUPPII(characterId);
      await updateUser_PUPPII(updatedUser);
      return true;
    }
    return false;
  }

  // 检查是否收藏了某个角色
  bool isFavoriteCharacter_PUPPII(String characterId) {
    final user = currentUser_PUPPII.value;
    return user?.isFavoriteCharacter_PUPPII(characterId) ?? false;
  }

  // 获取用户统计信息
  Map<String, dynamic> getUserStats_PUPPII() {
    final user = currentUser_PUPPII.value;
    if (user != null) {
      return UserData_PUPPII.getUserStats_PUPPII(user);
    }
    return {};
  }

  // 重置用户数据
  Future<void> resetUserData_PUPPII() async {
    final defaultUser = UserData_PUPPII.getDefaultUser_PUPPII();
    await updateUser_PUPPII(defaultUser);
  }

  // 清除用户数据
  Future<void> clearUserData_PUPPII() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userDataKey_PUPPII);
      currentUser_PUPPII.value = null;
    } catch (e) {
      print('[UserService] Error clearing user data: $e');
    }
  }

  // 获取当前用户
  UserModel_PUPPII? get user => currentUser_PUPPII.value;
  
  // 获取用户昵称
  String get nickname => currentUser_PUPPII.value?.nickname_PUPPII ?? 'Guest';
  
  // 获取用户头像路径
  String get avatarPath => currentUser_PUPPII.value?.avatarPath_PUPPII ?? 'assets_puppii/images_puppii/user_01.png';
  
  // 获取金币余额
  int get coinBalance => currentUser_PUPPII.value?.coinBalance_PUPPII ?? 0;
  
  // 获取收藏角色数量
  int get favoriteCount => currentUser_PUPPII.value?.favoriteCharacterIds_PUPPII.length ?? 0;
}
