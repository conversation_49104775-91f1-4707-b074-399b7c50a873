import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../models_PUPPII_efgh/store_item_model_PUPPII_qrst.dart';
import '../data_PUPPII_mnop/store_data_PUPPII_uvwx.dart';
import 'user_service_PUPPII_mnop.dart';

enum PurchaseState_PUPPII {
  idle,
  loading,
  pending,
  success,
  failed,
  cancelled,
  timeout,
}

class InAppPurchaseService_PUPPII extends GetxController {
  static InAppPurchaseService_PUPPII get instance => Get.find();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  // 响应式状态
  final Rx<PurchaseState_PUPPII> purchaseState_PUPPII = PurchaseState_PUPPII.idle.obs;
  final RxBool isStoreAvailable_PUPPII = false.obs;
  final RxList<ProductDetails> availableProducts_PUPPII = <ProductDetails>[].obs;
  final RxString currentPurchasingProductId_PUPPII = ''.obs;
  final RxBool isGlobalLoading_PUPPII = false.obs;

  // 购买超时计时器
  Timer? _purchaseTimeoutTimer_PUPPII;
  static const int _purchaseTimeoutSeconds_PUPPII = 50;

  @override
  void onInit() {
    super.onInit();
    _initializePurchaseService_PUPPII();
  }

  @override
  void onClose() {
    _subscription.cancel();
    _purchaseTimeoutTimer_PUPPII?.cancel();
    super.onClose();
  }

  // 初始化内购服务
  Future<void> _initializePurchaseService_PUPPII() async {
    try {
      // 检查内购是否可用
      isStoreAvailable_PUPPII.value = await _inAppPurchase.isAvailable();
      
      if (!isStoreAvailable_PUPPII.value) {
        print('[InAppPurchase] Store is not available');
        return;
      }

      // 监听购买状态变化
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdates_PUPPII,
        onDone: () => print('[InAppPurchase] Purchase stream done'),
        onError: (error) => print('[InAppPurchase] Purchase stream error: $error'),
      );

      print('[InAppPurchase] Service initialized successfully');
    } catch (e) {
      print('[InAppPurchase] Error initializing service: $e');
      isStoreAvailable_PUPPII.value = false;
    }
  }

  // 加载商品信息
  Future<void> loadProducts_PUPPII() async {
    if (!isStoreAvailable_PUPPII.value) {
      print('[InAppPurchase] Store not available, cannot load products');
      return;
    }

    try {
      final productIds = StoreData_PUPPII.getAllProductIds_PUPPII().toSet();
      final response = await _inAppPurchase.queryProductDetails(productIds);

      if (response.error != null) {
        print('[InAppPurchase] Error loading products: ${response.error}');
        return;
      }

      availableProducts_PUPPII.value = response.productDetails;
      print('[InAppPurchase] Loaded ${response.productDetails.length} products');
    } catch (e) {
      print('[InAppPurchase] Error loading products: $e');
    }
  }

  // 处理购买状态更新
  void _handlePurchaseUpdates_PUPPII(List<PurchaseDetails> purchaseDetailsList) {
    for (final purchaseDetails in purchaseDetailsList) {
      print('[InAppPurchase] Purchase update: ${purchaseDetails.status} for ${purchaseDetails.productID}');
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          _handlePendingPurchase_PUPPII(purchaseDetails);
          break;
        case PurchaseStatus.purchased:
          _handleSuccessfulPurchase_PUPPII(purchaseDetails);
          break;
        case PurchaseStatus.error:
          _handleFailedPurchase_PUPPII(purchaseDetails);
          break;
        case PurchaseStatus.canceled:
          _handleCancelledPurchase_PUPPII(purchaseDetails);
          break;
        case PurchaseStatus.restored:
          // 不处理恢复购买
          _completePurchase_PUPPII(purchaseDetails);
          break;
      }
    }
  }

  // 处理等待中的购买
  void _handlePendingPurchase_PUPPII(PurchaseDetails purchaseDetails) {
    purchaseState_PUPPII.value = PurchaseState_PUPPII.pending;
    // 取消超时计时器，因为用户已经在App Store界面
    _purchaseTimeoutTimer_PUPPII?.cancel();
  }

  // 处理成功的购买
  void _handleSuccessfulPurchase_PUPPII(PurchaseDetails purchaseDetails) async {
    _purchaseTimeoutTimer_PUPPII?.cancel();
    purchaseState_PUPPII.value = PurchaseState_PUPPII.success;
    isGlobalLoading_PUPPII.value = false;

    // 发放金币
    final storeItem = StoreData_PUPPII.getItemByProductId_PUPPII(purchaseDetails.productID);
    if (storeItem != null) {
      await UserService_PUPPII.instance.addCoins_PUPPII(storeItem.coinAmount_PUPPII);
      print('[InAppPurchase] Added ${storeItem.coinAmount_PUPPII} coins to user');
    }

    // 完成交易
    await _completePurchase_PUPPII(purchaseDetails);

    // 显示成功提示
    _showSuccessMessage_PUPPII(storeItem?.coinAmount_PUPPII ?? 0);
  }

  // 处理失败的购买
  void _handleFailedPurchase_PUPPII(PurchaseDetails purchaseDetails) async {
    _purchaseTimeoutTimer_PUPPII?.cancel();
    purchaseState_PUPPII.value = PurchaseState_PUPPII.failed;
    isGlobalLoading_PUPPII.value = false;

    await _completePurchase_PUPPII(purchaseDetails);
    _showErrorMessage_PUPPII('Purchase failed. Please try again.');
  }

  // 处理取消的购买
  void _handleCancelledPurchase_PUPPII(PurchaseDetails purchaseDetails) async {
    _purchaseTimeoutTimer_PUPPII?.cancel();
    purchaseState_PUPPII.value = PurchaseState_PUPPII.cancelled;
    isGlobalLoading_PUPPII.value = false;

    await _completePurchase_PUPPII(purchaseDetails);
  }

  // 完成购买交易
  Future<void> _completePurchase_PUPPII(PurchaseDetails purchaseDetails) async {
    if (purchaseDetails.pendingCompletePurchase) {
      await _inAppPurchase.completePurchase(purchaseDetails);
    }
    currentPurchasingProductId_PUPPII.value = '';
  }

  // 发起购买
  Future<void> purchaseProduct_PUPPII(String productId) async {
    if (!isStoreAvailable_PUPPII.value) {
      _showErrorMessage_PUPPII('Store is not available');
      return;
    }

    if (isGlobalLoading_PUPPII.value) {
      print('[InAppPurchase] Another purchase is in progress');
      return;
    }

    try {
      // 查找产品
      final product = availableProducts_PUPPII.firstWhereOrNull(
        (p) => p.id == productId,
      );

      if (product == null) {
        _showErrorMessage_PUPPII('Product not found');
        return;
      }

      // 设置购买状态
      purchaseState_PUPPII.value = PurchaseState_PUPPII.loading;
      isGlobalLoading_PUPPII.value = true;
      currentPurchasingProductId_PUPPII.value = productId;

      // 启动超时计时器
      _startPurchaseTimeout_PUPPII();

      // 发起购买请求
      final purchaseParam = PurchaseParam(productDetails: product);
      final success = await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);

      if (!success) {
        _handlePurchaseTimeout_PUPPII();
      }
    } catch (e) {
      print('[InAppPurchase] Error purchasing product: $e');
      _handlePurchaseTimeout_PUPPII();
    }
  }

  // 启动购买超时计时器
  void _startPurchaseTimeout_PUPPII() {
    _purchaseTimeoutTimer_PUPPII?.cancel();
    _purchaseTimeoutTimer_PUPPII = Timer(
      Duration(seconds: _purchaseTimeoutSeconds_PUPPII),
      _handlePurchaseTimeout_PUPPII,
    );
  }

  // 处理购买超时
  void _handlePurchaseTimeout_PUPPII() {
    if (purchaseState_PUPPII.value == PurchaseState_PUPPII.pending) {
      // 如果在pending状态，不处理超时
      return;
    }

    _purchaseTimeoutTimer_PUPPII?.cancel();
    purchaseState_PUPPII.value = PurchaseState_PUPPII.timeout;
    isGlobalLoading_PUPPII.value = false;
    currentPurchasingProductId_PUPPII.value = '';
    _showErrorMessage_PUPPII('Purchase timeout. Please try again.');
  }

  // 显示成功消息
  void _showSuccessMessage_PUPPII(int coinAmount) {
    Get.snackbar(
      'Purchase Successful',
      'You received $coinAmount coins!',
      backgroundColor: Colors.green.withOpacity(0.8),
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
    );
  }

  // 显示错误消息
  void _showErrorMessage_PUPPII(String message) {
    Get.snackbar(
      'Purchase Error',
      message,
      backgroundColor: Colors.red.withOpacity(0.8),
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
    );
  }

  // 重置购买状态
  void resetPurchaseState_PUPPII() {
    purchaseState_PUPPII.value = PurchaseState_PUPPII.idle;
    currentPurchasingProductId_PUPPII.value = '';
    isGlobalLoading_PUPPII.value = false;
    _purchaseTimeoutTimer_PUPPII?.cancel();
  }
}
