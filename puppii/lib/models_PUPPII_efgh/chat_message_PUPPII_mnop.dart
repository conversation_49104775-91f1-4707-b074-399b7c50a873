class ChatMessage_PUPPII {
  final String id_PUPPII;
  final String content_PUPPII;
  final bool isUser_PUPPII;
  final DateTime timestamp_PUPPII;
  final MessageStatus_PUPPII status_PUPPII;

  const ChatMessage_PUPPII({
    required this.id_PUPPII,
    required this.content_PUPPII,
    required this.isUser_PUPPII,
    required this.timestamp_PUPPII,
    this.status_PUPPII = MessageStatus_PUPPII.sent,
  });

  // 从JSON创建对象
  factory ChatMessage_PUPPII.fromJson(Map<String, dynamic> json) {
    return ChatMessage_PUPPII(
      id_PUPPII: json['id'] as String,
      content_PUPPII: json['content'] as String,
      isUser_PUPPII: json['isUser'] as bool,
      timestamp_PUPPII: DateTime.parse(json['timestamp'] as String),
      status_PUPPII: MessageStatus_PUPPII.values[json['status'] as int],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id_PUPPII,
      'content': content_PUPPII,
      'isUser': isUser_PUPPII,
      'timestamp': timestamp_PUPPII.toIso8601String(),
      'status': status_PUPPII.index,
    };
  }

  // 复制对象并修改某些属性
  ChatMessage_PUPPII copyWith({
    String? id_PUPPII,
    String? content_PUPPII,
    bool? isUser_PUPPII,
    DateTime? timestamp_PUPPII,
    MessageStatus_PUPPII? status_PUPPII,
  }) {
    return ChatMessage_PUPPII(
      id_PUPPII: id_PUPPII ?? this.id_PUPPII,
      content_PUPPII: content_PUPPII ?? this.content_PUPPII,
      isUser_PUPPII: isUser_PUPPII ?? this.isUser_PUPPII,
      timestamp_PUPPII: timestamp_PUPPII ?? this.timestamp_PUPPII,
      status_PUPPII: status_PUPPII ?? this.status_PUPPII,
    );
  }

  @override
  String toString() {
    return 'ChatMessage_PUPPII(id: $id_PUPPII, content: $content_PUPPII, isUser: $isUser_PUPPII, status: $status_PUPPII)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage_PUPPII && other.id_PUPPII == id_PUPPII;
  }

  @override
  int get hashCode => id_PUPPII.hashCode;
}

enum MessageStatus_PUPPII {
  sending,
  sent,
  failed,
  typing, // AI正在输入
}
