class CharacterModel_PUPPII {
  final String id_PUPPII;
  final String nickname_PUPPII;
  final String avatarPath_PUPPII;
  final String shortIntroduction_PUPPII;
  final String longIntroduction_PUPPII;
  final int freeChatCount_PUPPII;

  const CharacterModel_PUPPII({
    required this.id_PUPPII,
    required this.nickname_PUPPII,
    required this.avatarPath_PUPPII,
    required this.shortIntroduction_PUPPII,
    required this.longIntroduction_PUPPII,
    required this.freeChatCount_PUPPII,
  });

  // 从JSON创建对象
  factory CharacterModel_PUPPII.fromJson(Map<String, dynamic> json) {
    return CharacterModel_PUPPII(
      id_PUPPII: json['id'] as String,
      nickname_PUPPII: json['nickname'] as String,
      avatarPath_PUPPII: json['avatarPath'] as String,
      shortIntroduction_PUPPII: json['shortIntroduction'] as String,
      longIntroduction_PUPPII: json['longIntroduction'] as String,
      freeChatCount_PUPPII: json['freeChatCount'] as int,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id_PUPPII,
      'nickname': nickname_PUPPII,
      'avatarPath': avatarPath_PUPPII,
      'shortIntroduction': shortIntroduction_PUPPII,
      'longIntroduction': longIntroduction_PUPPII,
      'freeChatCount': freeChatCount_PUPPII,
    };
  }

  // 复制对象并修改某些属性
  CharacterModel_PUPPII copyWith({
    String? id_PUPPII,
    String? nickname_PUPPII,
    String? avatarPath_PUPPII,
    String? shortIntroduction_PUPPII,
    String? longIntroduction_PUPPII,
    int? freeChatCount_PUPPII,
  }) {
    return CharacterModel_PUPPII(
      id_PUPPII: id_PUPPII ?? this.id_PUPPII,
      nickname_PUPPII: nickname_PUPPII ?? this.nickname_PUPPII,
      avatarPath_PUPPII: avatarPath_PUPPII ?? this.avatarPath_PUPPII,
      shortIntroduction_PUPPII: shortIntroduction_PUPPII ?? this.shortIntroduction_PUPPII,
      longIntroduction_PUPPII: longIntroduction_PUPPII ?? this.longIntroduction_PUPPII,
      freeChatCount_PUPPII: freeChatCount_PUPPII ?? this.freeChatCount_PUPPII,
    );
  }

  @override
  String toString() {
    return 'CharacterModel_PUPPII(id: $id_PUPPII, nickname: $nickname_PUPPII, shortIntroduction: $shortIntroduction_PUPPII)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CharacterModel_PUPPII && other.id_PUPPII == id_PUPPII;
  }

  @override
  int get hashCode => id_PUPPII.hashCode;
}
