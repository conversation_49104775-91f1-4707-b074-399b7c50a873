class UserModel_PUPPII {
  final String id_PUPPII;
  final String nickname_PUPPII;
  final String avatarPath_PUPPII;
  final int coinBalance_PUPPII;
  final Set<String> favoriteCharacterIds_PUPPII;

  const UserModel_PUPPII({
    required this.id_PUPPII,
    required this.nickname_PUPPII,
    required this.avatarPath_PUPPII,
    required this.coinBalance_PUPPII,
    required this.favoriteCharacterIds_PUPPII,
  });

  // 从JSON创建用户模型
  factory UserModel_PUPPII.fromJson(Map<String, dynamic> json) {
    return UserModel_PUPPII(
      id_PUPPII: json['id_PUPPII'] as String,
      nickname_PUPPII: json['nickname_PUPPII'] as String,
      avatarPath_PUPPII: json['avatarPath_PUPPII'] as String,
      coinBalance_PUPPII: json['coinBalance_PUPPII'] as int,
      favoriteCharacterIds_PUPPII: Set<String>.from(
        json['favoriteCharacterIds_PUPPII'] as List<dynamic>? ?? [],
      ),
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id_PUPPII': id_PUPPII,
      'nickname_PUPPII': nickname_PUPPII,
      'avatarPath_PUPPII': avatarPath_PUPPII,
      'coinBalance_PUPPII': coinBalance_PUPPII,
      'favoriteCharacterIds_PUPPII': favoriteCharacterIds_PUPPII.toList(),
    };
  }

  // 复制并修改部分属性
  UserModel_PUPPII copyWith({
    String? id_PUPPII,
    String? nickname_PUPPII,
    String? avatarPath_PUPPII,
    int? coinBalance_PUPPII,
    Set<String>? favoriteCharacterIds_PUPPII,
  }) {
    return UserModel_PUPPII(
      id_PUPPII: id_PUPPII ?? this.id_PUPPII,
      nickname_PUPPII: nickname_PUPPII ?? this.nickname_PUPPII,
      avatarPath_PUPPII: avatarPath_PUPPII ?? this.avatarPath_PUPPII,
      coinBalance_PUPPII: coinBalance_PUPPII ?? this.coinBalance_PUPPII,
      favoriteCharacterIds_PUPPII: favoriteCharacterIds_PUPPII ?? this.favoriteCharacterIds_PUPPII,
    );
  }

  // 添加收藏角色
  UserModel_PUPPII addFavoriteCharacter_PUPPII(String characterId) {
    final newFavorites = Set<String>.from(favoriteCharacterIds_PUPPII);
    newFavorites.add(characterId);
    return copyWith(favoriteCharacterIds_PUPPII: newFavorites);
  }

  // 移除收藏角色
  UserModel_PUPPII removeFavoriteCharacter_PUPPII(String characterId) {
    final newFavorites = Set<String>.from(favoriteCharacterIds_PUPPII);
    newFavorites.remove(characterId);
    return copyWith(favoriteCharacterIds_PUPPII: newFavorites);
  }

  // 检查是否收藏了某个角色
  bool isFavoriteCharacter_PUPPII(String characterId) {
    return favoriteCharacterIds_PUPPII.contains(characterId);
  }

  // 增加金币
  UserModel_PUPPII addCoins_PUPPII(int amount) {
    return copyWith(coinBalance_PUPPII: coinBalance_PUPPII + amount);
  }

  // 减少金币
  UserModel_PUPPII subtractCoins_PUPPII(int amount) {
    final newBalance = coinBalance_PUPPII - amount;
    return copyWith(coinBalance_PUPPII: newBalance < 0 ? 0 : newBalance);
  }

  // 检查是否有足够金币
  bool hasEnoughCoins_PUPPII(int amount) {
    return coinBalance_PUPPII >= amount;
  }

  @override
  String toString() {
    return 'UserModel_PUPPII(id: $id_PUPPII, nickname: $nickname_PUPPII, coins: $coinBalance_PUPPII, favorites: ${favoriteCharacterIds_PUPPII.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel_PUPPII &&
        other.id_PUPPII == id_PUPPII &&
        other.nickname_PUPPII == nickname_PUPPII &&
        other.avatarPath_PUPPII == avatarPath_PUPPII &&
        other.coinBalance_PUPPII == coinBalance_PUPPII &&
        other.favoriteCharacterIds_PUPPII.length == favoriteCharacterIds_PUPPII.length &&
        other.favoriteCharacterIds_PUPPII.containsAll(favoriteCharacterIds_PUPPII);
  }

  @override
  int get hashCode {
    return Object.hash(
      id_PUPPII,
      nickname_PUPPII,
      avatarPath_PUPPII,
      coinBalance_PUPPII,
      favoriteCharacterIds_PUPPII.length,
    );
  }
}
