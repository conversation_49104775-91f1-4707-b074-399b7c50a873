import 'character_model_PUPPII_ijkl.dart';
import 'chat_message_PUPPII_mnop.dart';

class ChatSession_PUPPII {
  final String sessionId_PUPPII;
  final CharacterModel_PUPPII character_PUPPII;
  final List<ChatMessage_PUPPII> messages_PUPPII;
  final DateTime lastUpdated_PUPPII;
  final bool isFavorite_PUPPII;
  final int remainingChats_PUPPII;

  const ChatSession_PUPPII({
    required this.sessionId_PUPPII,
    required this.character_PUPPII,
    required this.messages_PUPPII,
    required this.lastUpdated_PUPPII,
    this.isFavorite_PUPPII = false,
    required this.remainingChats_PUPPII,
  });

  // 从JSON创建对象
  factory ChatSession_PUPPII.fromJson(Map<String, dynamic> json) {
    return ChatSession_PUPPII(
      sessionId_PUPPII: json['sessionId'] as String,
      character_PUPPII: CharacterModel_PUPPII.fromJson(json['character'] as Map<String, dynamic>),
      messages_PUPPII: (json['messages'] as List)
          .map((msg) => ChatMessage_PUPPII.fromJson(msg as Map<String, dynamic>))
          .toList(),
      lastUpdated_PUPPII: DateTime.parse(json['lastUpdated'] as String),
      isFavorite_PUPPII: json['isFavorite'] as bool? ?? false,
      remainingChats_PUPPII: json['remainingChats'] as int,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId_PUPPII,
      'character': character_PUPPII.toJson(),
      'messages': messages_PUPPII.map((msg) => msg.toJson()).toList(),
      'lastUpdated': lastUpdated_PUPPII.toIso8601String(),
      'isFavorite': isFavorite_PUPPII,
      'remainingChats': remainingChats_PUPPII,
    };
  }

  // 复制对象并修改某些属性
  ChatSession_PUPPII copyWith({
    String? sessionId_PUPPII,
    CharacterModel_PUPPII? character_PUPPII,
    List<ChatMessage_PUPPII>? messages_PUPPII,
    DateTime? lastUpdated_PUPPII,
    bool? isFavorite_PUPPII,
    int? remainingChats_PUPPII,
  }) {
    return ChatSession_PUPPII(
      sessionId_PUPPII: sessionId_PUPPII ?? this.sessionId_PUPPII,
      character_PUPPII: character_PUPPII ?? this.character_PUPPII,
      messages_PUPPII: messages_PUPPII ?? this.messages_PUPPII,
      lastUpdated_PUPPII: lastUpdated_PUPPII ?? this.lastUpdated_PUPPII,
      isFavorite_PUPPII: isFavorite_PUPPII ?? this.isFavorite_PUPPII,
      remainingChats_PUPPII: remainingChats_PUPPII ?? this.remainingChats_PUPPII,
    );
  }

  @override
  String toString() {
    return 'ChatSession_PUPPII(sessionId: $sessionId_PUPPII, character: ${character_PUPPII.nickname_PUPPII}, messagesCount: ${messages_PUPPII.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatSession_PUPPII && other.sessionId_PUPPII == sessionId_PUPPII;
  }

  @override
  int get hashCode => sessionId_PUPPII.hashCode;
}
