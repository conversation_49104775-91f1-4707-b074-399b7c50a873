class StoreItemModel_PUPPII {
  final String productId_PUPPII;
  final int coinAmount_PUPPII;
  final String price_PUPPII;
  final String tag_PUPPII;

  const StoreItemModel_PUPPII({
    required this.productId_PUPPII,
    required this.coinAmount_PUPPII,
    required this.price_PUPPII,
    required this.tag_PUPPII,
  });

  // 从JSON创建商品模型
  factory StoreItemModel_PUPPII.fromJson(Map<String, dynamic> json) {
    return StoreItemModel_PUPPII(
      productId_PUPPII: json['productId_PUPPII'] as String,
      coinAmount_PUPPII: json['coinAmount_PUPPII'] as int,
      price_PUPPII: json['price_PUPPII'] as String,
      tag_PUPPII: json['tag_PUPPII'] as String,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'productId_PUPPII': productId_PUPPII,
      'coinAmount_PUPPII': coinAmount_PUPPII,
      'price_PUPPII': price_PUPPII,
      'tag_PUPPII': tag_PUPPII,
    };
  }

  // 复制并修改部分属性
  StoreItemModel_PUPPII copyWith({
    String? productId_PUPPII,
    int? coinAmount_PUPPII,
    String? price_PUPPII,
    String? tag_PUPPII,
  }) {
    return StoreItemModel_PUPPII(
      productId_PUPPII: productId_PUPPII ?? this.productId_PUPPII,
      coinAmount_PUPPII: coinAmount_PUPPII ?? this.coinAmount_PUPPII,
      price_PUPPII: price_PUPPII ?? this.price_PUPPII,
      tag_PUPPII: tag_PUPPII ?? this.tag_PUPPII,
    );
  }

  // 是否有促销标签
  bool get hasTag_PUPPII => tag_PUPPII.isNotEmpty;

  // 格式化价格显示
  String get formattedPrice_PUPPII => '\$ ${price_PUPPII}';

  // 格式化金币数量显示
  String get formattedCoinAmount_PUPPII {
    if (coinAmount_PUPPII >= 1000) {
      final thousands = coinAmount_PUPPII / 1000;
      if (thousands == thousands.toInt()) {
        return '${thousands.toInt()}K';
      } else {
        return '${thousands.toStringAsFixed(1)}K';
      }
    }
    return coinAmount_PUPPII.toString();
  }

  @override
  String toString() {
    return 'StoreItemModel_PUPPII(productId: $productId_PUPPII, coins: $coinAmount_PUPPII, price: $price_PUPPII, tag: $tag_PUPPII)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoreItemModel_PUPPII &&
        other.productId_PUPPII == productId_PUPPII &&
        other.coinAmount_PUPPII == coinAmount_PUPPII &&
        other.price_PUPPII == price_PUPPII &&
        other.tag_PUPPII == tag_PUPPII;
  }

  @override
  int get hashCode {
    return Object.hash(
      productId_PUPPII,
      coinAmount_PUPPII,
      price_PUPPII,
      tag_PUPPII,
    );
  }
}
