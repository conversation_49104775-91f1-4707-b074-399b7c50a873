class SoulQuestionModel_PUPPII {
  final String id_PUPPII;
  final String question_PUPPII;
  final String category_PUPPII;
  final int rewardCoins_PUPPII;

  const SoulQuestionModel_PUPPII({
    required this.id_PUPPII,
    required this.question_PUPPII,
    required this.category_PUPPII,
    this.rewardCoins_PUPPII = 10,
  });

  Map<String, dynamic> toJson_PUPPII() {
    return {
      'id': id_PUPPII,
      'question': question_PUPPII,
      'category': category_PUPPII,
      'rewardCoins': rewardCoins_PUPPII,
    };
  }

  factory SoulQuestionModel_PUPPII.fromJson_PUPPII(Map<String, dynamic> json) {
    return SoulQuestionModel_PUPPII(
      id_PUPPII: json['id'] ?? '',
      question_PUPPII: json['question'] ?? '',
      category_PUPPII: json['category'] ?? '',
      rewardCoins_PUPPII: json['rewardCoins'] ?? 10,
    );
  }
}

class SoulQuestionAnswer_PUPPII {
  final String questionId_PUPPII;
  final String answer_PUPPII;
  final DateTime answeredAt_PUPPII;
  final int coinsEarned_PUPPII;

  const SoulQuestionAnswer_PUPPII({
    required this.questionId_PUPPII,
    required this.answer_PUPPII,
    required this.answeredAt_PUPPII,
    required this.coinsEarned_PUPPII,
  });

  Map<String, dynamic> toJson_PUPPII() {
    return {
      'questionId': questionId_PUPPII,
      'answer': answer_PUPPII,
      'answeredAt': answeredAt_PUPPII.toIso8601String(),
      'coinsEarned': coinsEarned_PUPPII,
    };
  }

  factory SoulQuestionAnswer_PUPPII.fromJson_PUPPII(Map<String, dynamic> json) {
    return SoulQuestionAnswer_PUPPII(
      questionId_PUPPII: json['questionId'] ?? '',
      answer_PUPPII: json['answer'] ?? '',
      answeredAt_PUPPII: DateTime.parse(json['answeredAt']),
      coinsEarned_PUPPII: json['coinsEarned'] ?? 10,
    );
  }
}

class SoulQuestionData_PUPPII {
  static const List<SoulQuestionModel_PUPPII> questions_PUPPII = [
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_001_PUPPII',
      question_PUPPII: 'What recent event has touched your heart the most?',
      category_PUPPII: 'emotion',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_002_PUPPII',
      question_PUPPII: 'If you could have a conversation with your past self, what would you say?',
      category_PUPPII: 'reflection',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_003_PUPPII',
      question_PUPPII: 'What small moment today made you feel grateful?',
      category_PUPPII: 'gratitude',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_004_PUPPII',
      question_PUPPII: 'What fear have you overcome recently, and how did it change you?',
      category_PUPPII: 'growth',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_005_PUPPII',
      question_PUPPII: 'What does happiness mean to you right now in your life?',
      category_PUPPII: 'philosophy',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_006_PUPPII',
      question_PUPPII: 'Who has influenced you the most this year, and why?',
      category_PUPPII: 'relationships',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_007_PUPPII',
      question_PUPPII: 'What dream are you afraid to pursue, and what holds you back?',
      category_PUPPII: 'dreams',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_008_PUPPII',
      question_PUPPII: 'What lesson did you learn the hard way recently?',
      category_PUPPII: 'wisdom',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_009_PUPPII',
      question_PUPPII: 'If today was your last day, what would you regret not saying?',
      category_PUPPII: 'urgency',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_010_PUPPII',
      question_PUPPII: 'What simple pleasure brings you the most joy?',
      category_PUPPII: 'joy',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_011_PUPPII',
      question_PUPPII: 'How have you changed in the past year, and are you proud of it?',
      category_PUPPII: 'growth',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_012_PUPPII',
      question_PUPPII: 'What would you do if you knew you could not fail?',
      category_PUPPII: 'courage',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_013_PUPPII',
      question_PUPPII: 'What memory from childhood still makes you smile?',
      category_PUPPII: 'nostalgia',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_014_PUPPII',
      question_PUPPII: 'What act of kindness have you witnessed recently?',
      category_PUPPII: 'kindness',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_015_PUPPII',
      question_PUPPII: 'What would you tell someone who is going through what you went through?',
      category_PUPPII: 'empathy',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_016_PUPPII',
      question_PUPPII: 'What tradition or ritual brings meaning to your life?',
      category_PUPPII: 'meaning',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_017_PUPPII',
      question_PUPPII: 'What would you do with an extra hour every day?',
      category_PUPPII: 'priorities',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_018_PUPPII',
      question_PUPPII: 'What quality do you admire most in others?',
      category_PUPPII: 'values',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_019_PUPPII',
      question_PUPPII: 'What moment made you realize you were stronger than you thought?',
      category_PUPPII: 'strength',
    ),
    SoulQuestionModel_PUPPII(
      id_PUPPII: 'sq_020_PUPPII',
      question_PUPPII: 'What would you want to be remembered for?',
      category_PUPPII: 'legacy',
    ),
  ];

  static SoulQuestionModel_PUPPII getRandomQuestion_PUPPII() {
    final random = DateTime.now().millisecondsSinceEpoch % questions_PUPPII.length;
    return questions_PUPPII[random];
  }

  static List<SoulQuestionModel_PUPPII> getAllQuestions_PUPPII() {
    return questions_PUPPII;
  }
}
