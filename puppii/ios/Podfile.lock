PODS:
  - audio_session (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - record_ios (1.0.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - TOCropViewController (2.6.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - Flutter (from `Flutter`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_ios (from `.symlinks/plugins/record_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - TOCropViewController

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  Flutter:
    :path: Flutter
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_ios:
    :path: ".symlinks/plugins/record_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  image_cropper: a3291c624a953049bc6a02e1f8c8ceb162a24b25
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_purchase_storekit: a1ce04056e23eecc666b086040239da7619cd783
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  record_ios: 1bbc430ab8406174d70332f23e06a3dc751239b4
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
