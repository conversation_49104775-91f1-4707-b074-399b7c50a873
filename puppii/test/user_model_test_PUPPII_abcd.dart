import 'package:flutter_test/flutter_test.dart';
import 'package:puppii/models_PUPPII_efgh/user_model_PUPPII_wxyz.dart';
import 'package:puppii/data_PUPPII_mnop/user_data_PUPPII_abcd.dart';

void main() {
  group('UserModel_PUPPII Tests', () {
    test('should create user model with correct properties', () {
      const user = UserModel_PUPPII(
        id_PUPPII: 'test_user_001',
        nickname_PUPPII: 'Test User',
        avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
        coinBalance_PUPPII: 100,
        favoriteCharacterIds_PUPPII: <String>{},
      );

      expect(user.id_PUPPII, 'test_user_001');
      expect(user.nickname_PUPPII, 'Test User');
      expect(user.avatarPath_PUPPII, 'assets_puppii/images_puppii/user_01.png');
      expect(user.coinBalance_PUPPII, 100);
      expect(user.favoriteCharacterIds_PUPPII.isEmpty, true);
    });

    test('should convert to and from JSON correctly', () {
      const originalUser = UserModel_PUPPII(
        id_PUPPII: 'test_user_001',
        nickname_PUPPII: 'Test User',
        avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
        coinBalance_PUPPII: 100,
        favoriteCharacterIds_PUPPII: {'char1', 'char2'},
      );

      final json = originalUser.toJson();
      final reconstructedUser = UserModel_PUPPII.fromJson(json);

      expect(reconstructedUser.id_PUPPII, originalUser.id_PUPPII);
      expect(reconstructedUser.nickname_PUPPII, originalUser.nickname_PUPPII);
      expect(reconstructedUser.avatarPath_PUPPII, originalUser.avatarPath_PUPPII);
      expect(reconstructedUser.coinBalance_PUPPII, originalUser.coinBalance_PUPPII);
      expect(reconstructedUser.favoriteCharacterIds_PUPPII, originalUser.favoriteCharacterIds_PUPPII);
    });

    test('should add and remove favorite characters correctly', () {
      const user = UserModel_PUPPII(
        id_PUPPII: 'test_user_001',
        nickname_PUPPII: 'Test User',
        avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
        coinBalance_PUPPII: 100,
        favoriteCharacterIds_PUPPII: <String>{},
      );

      // Add favorite character
      final userWithFavorite = user.addFavoriteCharacter_PUPPII('char1');
      expect(userWithFavorite.favoriteCharacterIds_PUPPII.contains('char1'), true);
      expect(userWithFavorite.isFavoriteCharacter_PUPPII('char1'), true);

      // Remove favorite character
      final userWithoutFavorite = userWithFavorite.removeFavoriteCharacter_PUPPII('char1');
      expect(userWithoutFavorite.favoriteCharacterIds_PUPPII.contains('char1'), false);
      expect(userWithoutFavorite.isFavoriteCharacter_PUPPII('char1'), false);
    });

    test('should handle coin operations correctly', () {
      const user = UserModel_PUPPII(
        id_PUPPII: 'test_user_001',
        nickname_PUPPII: 'Test User',
        avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
        coinBalance_PUPPII: 100,
        favoriteCharacterIds_PUPPII: <String>{},
      );

      // Add coins
      final userWithMoreCoins = user.addCoins_PUPPII(50);
      expect(userWithMoreCoins.coinBalance_PUPPII, 150);

      // Subtract coins
      final userWithFewerCoins = userWithMoreCoins.subtractCoins_PUPPII(30);
      expect(userWithFewerCoins.coinBalance_PUPPII, 120);

      // Check if has enough coins
      expect(userWithFewerCoins.hasEnoughCoins_PUPPII(100), true);
      expect(userWithFewerCoins.hasEnoughCoins_PUPPII(150), false);

      // Subtract more coins than available (should not go below 0)
      final userWithZeroCoins = userWithFewerCoins.subtractCoins_PUPPII(200);
      expect(userWithZeroCoins.coinBalance_PUPPII, 0);
    });
  });

  group('UserData_PUPPII Tests', () {
    test('should create default user correctly', () {
      final defaultUser = UserData_PUPPII.getDefaultUser_PUPPII();
      
      expect(defaultUser.id_PUPPII, 'user_default_001_PUPPII');
      expect(defaultUser.nickname_PUPPII, 'Alex Chen');
      expect(defaultUser.avatarPath_PUPPII, 'assets_puppii/images_puppii/user_01.png');
      expect(defaultUser.coinBalance_PUPPII, 100);
      expect(defaultUser.favoriteCharacterIds_PUPPII.isEmpty, true);
    });

    test('should validate user data correctly', () {
      // Valid user
      const validUser = UserModel_PUPPII(
        id_PUPPII: 'test_user_001',
        nickname_PUPPII: 'Test User',
        avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
        coinBalance_PUPPII: 100,
        favoriteCharacterIds_PUPPII: <String>{},
      );
      expect(UserData_PUPPII.isValidUserData_PUPPII(validUser), true);

      // Invalid user - empty ID
      const invalidUserEmptyId = UserModel_PUPPII(
        id_PUPPII: '',
        nickname_PUPPII: 'Test User',
        avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
        coinBalance_PUPPII: 100,
        favoriteCharacterIds_PUPPII: <String>{},
      );
      expect(UserData_PUPPII.isValidUserData_PUPPII(invalidUserEmptyId), false);

      // Invalid user - negative coins
      const invalidUserNegativeCoins = UserModel_PUPPII(
        id_PUPPII: 'test_user_001',
        nickname_PUPPII: 'Test User',
        avatarPath_PUPPII: 'assets_puppii/images_puppii/user_01.png',
        coinBalance_PUPPII: -10,
        favoriteCharacterIds_PUPPII: <String>{},
      );
      expect(UserData_PUPPII.isValidUserData_PUPPII(invalidUserNegativeCoins), false);
    });

    test('should calculate user level correctly', () {
      expect(UserData_PUPPII.getUserLevel_PUPPII(1000), 5); // Diamond
      expect(UserData_PUPPII.getUserLevel_PUPPII(500), 4);  // Gold
      expect(UserData_PUPPII.getUserLevel_PUPPII(200), 3);  // Silver
      expect(UserData_PUPPII.getUserLevel_PUPPII(50), 2);   // Bronze
      expect(UserData_PUPPII.getUserLevel_PUPPII(10), 1);   // Rookie
    });

    test('should get user level names correctly', () {
      expect(UserData_PUPPII.getUserLevelName_PUPPII(5), 'Diamond');
      expect(UserData_PUPPII.getUserLevelName_PUPPII(4), 'Gold');
      expect(UserData_PUPPII.getUserLevelName_PUPPII(3), 'Silver');
      expect(UserData_PUPPII.getUserLevelName_PUPPII(2), 'Bronze');
      expect(UserData_PUPPII.getUserLevelName_PUPPII(1), 'Rookie');
      expect(UserData_PUPPII.getUserLevelName_PUPPII(0), 'Unknown');
    });
  });
}
