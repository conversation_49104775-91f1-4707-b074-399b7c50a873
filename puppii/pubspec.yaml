name: puppii
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  
  # State Management
  get: ^4.6.6
  
  # Network & HTTP
  dio: ^5.4.0
  
  # Local Storage
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  
  # UI & Responsive Design
  flutter_screenutil: ^5.9.0
  google_fonts: ^6.1.0
  top_snackbar_flutter: ^1.0.0
  flutter_staggered_grid_view: ^0.7.0
  
  # Image Handling
  image_picker: ^1.0.4
  photo_view: ^0.14.0
  image_cropper: ^5.0.1
  
  # Permissions
  permission_handler: ^11.0.1
  
  # Audio
  audio_session: ^0.1.18
  record: ^6.0.0
  audioplayers: ^5.2.1
  
  # In-App Purchase
  in_app_purchase: ^3.1.0
  
  # Web & URL
  url_launcher: ^6.2.1
  webview_flutter: ^4.4.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  flutter_launcher_icons: "^0.13.1" 

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  uses-material-design: true

  assets:
    - assets_puppii/
    - assets_puppii/icons_puppii/
    - assets_puppii/images_puppii/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets_puppii/icons_puppii/logo.png"
  remove_alpha_channel: true