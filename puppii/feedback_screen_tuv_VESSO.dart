import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';

class FeedbackScreen_tuv_VESSO extends StatefulWidget {
  const FeedbackScreen_tuv_VESSO({super.key});

  @override
  State<FeedbackScreen_tuv_VESSO> createState() =>
      _FeedbackScreen_tuv_VESSOState();
}

class _FeedbackScreen_tuv_VESSOState extends State<FeedbackScreen_tuv_VESSO> {
  final TextEditingController _textController_VESSO = TextEditingController();
  final ImagePicker _imagePicker_VESSO = ImagePicker();
  final AudioRecorder _audioRecorder_VESSO = AudioRecorder();
  final AudioPlayer _audioPlayer_VESSO = AudioPlayer();

  List<File> _selectedImages_VESSO = [];
  String? _audioPath_VESSO;
  bool _isRecording_VESSO = false;
  bool _isPlaying_VESSO = false;
  Duration _recordingDuration_VESSO = Duration.zero;
  Timer? _recordingTimer_VESSO;

  @override
  void dispose() {
    _textController_VESSO.dispose();
    _audioRecorder_VESSO.dispose();
    _audioPlayer_VESSO.dispose();
    _recordingTimer_VESSO?.cancel();
    super.dispose();
  }

  bool get _hasContent_VESSO =>
      _textController_VESSO.text.trim().isNotEmpty ||
      _audioPath_VESSO != null ||
      _selectedImages_VESSO.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F1EB), // 简单的奶茶色背景
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              margin: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: const Color(0xFFE8C4A0),
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black,
                    offset: Offset(3.w, 3.h),
                    blurRadius: 0,
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 18.sp,
              ),
            ),
          ),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'FEEDBACK',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w900,
                  color: const Color(0xFFE8C4A0),
                  letterSpacing: 1.2,
                  shadows: [
                    Shadow(
                      color: Colors.black,
                      offset: Offset(2.w, 2.h),
                      blurRadius: 0,
                    ),
                  ],
                ),
              ),
              Text(
                'Tell us your thoughts',
                style: GoogleFonts.inter(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFFD4A574),
                ),
              ),
            ],
          ),
          toolbarHeight: 80.h,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(20.w),
          child: Column(
            children: [
              // Voice Feedback (放在最上面，突出差异)
              _buildVoiceFeedback_VESSO(),
              SizedBox(height: 20.h),

              // Image Upload (第二个位置)
              _buildImageUpload_VESSO(),
              SizedBox(height: 20.h),

              // Text Feedback (最后)
              _buildTextFeedback_VESSO(),
              SizedBox(height: 32.h),

              // Submit Button
              _buildSubmitButton_VESSO(),
              SizedBox(height: 40.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextFeedback_VESSO() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: const Color(0xFFE8C4A0),
          width: 3.w,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black,
            offset: Offset(6.w, 6.h),
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with icon and title
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: const Color(0xFFE8C4A0),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(13.r),
                topRight: Radius.circular(13.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black,
                        offset: Offset(2.w, 2.h),
                        blurRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.chat_bubble_outline,
                    color: const Color(0xFFE8C4A0),
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: 12.w),
                Text(
                  'Written Feedback',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    '${_textController_VESSO.text.length}/500',
                    style: GoogleFonts.inter(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Text input area
          Padding(
            padding: EdgeInsets.all(16.w),
            child: TextField(
              controller: _textController_VESSO,
              maxLines: 6,
              maxLength: 500,
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: const Color(0xFF2D2D2D),
                height: 1.4,
              ),
              decoration: InputDecoration(
                hintText:
                    'Share your thoughts, suggestions, or report any issues you encountered...',
                hintStyle: GoogleFonts.inter(
                  fontSize: 14.sp,
                  color: const Color(0xFF999999),
                  height: 1.4,
                ),
                border: InputBorder.none,
                counterText: '', // Hide default counter
                contentPadding: EdgeInsets.zero,
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceFeedback_VESSO() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: const Color(0xFFE8C4A0),
          width: 3.w,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black,
            offset: Offset(6.w, 6.h),
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: const Color(0xFFE8C4A0),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(13.r),
                topRight: Radius.circular(13.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black,
                        offset: Offset(2.w, 2.h),
                        blurRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.keyboard_voice,
                    color: const Color(0xFFE8C4A0),
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'Voice Message',
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                if (_isRecording_VESSO)
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black,
                          offset: Offset(2.w, 2.h),
                          blurRadius: 0,
                        ),
                      ],
                    ),
                    child: Text(
                      '${_recordingDuration_VESSO.inMinutes.toString().padLeft(2, '0')}:${(_recordingDuration_VESSO.inSeconds % 60).toString().padLeft(2, '0')}',
                      style: GoogleFonts.inter(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Content area
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              children: [
                // Status text
                Text(
                  _audioPath_VESSO == null
                      ? (_isRecording_VESSO
                          ? 'Recording in progress... Tap the button below to stop.'
                          : 'Tap the button below to start recording your voice feedback.')
                      : 'Voice message recorded successfully! You can play it back or delete it.',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 14.sp,
                    color: const Color(0xFF666666),
                    height: 1.4,
                  ),
                ),

                SizedBox(height: 20.h),

                // Record/Play button
                _audioPath_VESSO == null
                    ? _buildRecordButton_VESSO()
                    : _buildAudioPlayer_VESSO(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordButton_VESSO() {
    return GestureDetector(
      onTap: _isRecording_VESSO ? _stopRecording_VESSO : _startRecording_VESSO,
      child: Container(
        width: 70.w,
        height: 70.w,
        decoration: BoxDecoration(
          color: _isRecording_VESSO ? Colors.red : const Color(0xFFE8C4A0),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: Colors.white,
            width: 4.w,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black,
              offset: Offset(4.w, 4.h),
              blurRadius: 0,
            ),
          ],
        ),
        child: Icon(
          _isRecording_VESSO ? Icons.stop_rounded : Icons.mic_rounded,
          color: Colors.white,
          size: 28.sp,
        ),
      ),
    );
  }

  Widget _buildAudioPlayer_VESSO() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Play button
        Center(
          child: GestureDetector(
            onTap: _togglePlayback_VESSO,
            child: Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: const Color(0xFFE8C4A0),
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: Colors.white,
                  width: 4.w,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black,
                    offset: Offset(4.w, 4.h),
                    blurRadius: 0,
                  ),
                ],
              ),
              child: Icon(
                _isPlaying_VESSO
                    ? Icons.pause_rounded
                    : Icons.play_arrow_rounded,
                color: Colors.white,
                size: 32.sp,
              ),
            ),
          ),
        ),

        SizedBox(height: 16.h),

        // Control buttons row
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Delete button
            GestureDetector(
              onTap: _deleteAudio_VESSO,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: Colors.white,
                    width: 2.w,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black,
                      offset: Offset(3.w, 3.h),
                      blurRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.delete_rounded,
                      color: Colors.white,
                      size: 16.sp,
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      'Delete',
                      style: GoogleFonts.inter(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildImageUpload_VESSO() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: const Color(0xFFE8C4A0),
          width: 3.w,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black,
            offset: Offset(6.w, 6.h),
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: const Color(0xFFE8C4A0),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(13.r),
                topRight: Radius.circular(13.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black,
                        offset: Offset(2.w, 2.h),
                        blurRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.image_outlined,
                    color: const Color(0xFFE8C4A0),
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: 12.w),
                Text(
                  'Photo Attachments',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    '${_selectedImages_VESSO.length}/6',
                    style: GoogleFonts.inter(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: EdgeInsets.all(16.w),
            child: _selectedImages_VESSO.isEmpty
                ? _buildImagePickerButtons_VESSO()
                : Column(
                    children: [
                      _buildImageGrid_VESSO(),
                      if (_selectedImages_VESSO.length < 6) ...[
                        SizedBox(height: 12.h),
                        _buildAddMoreButton_VESSO(),
                      ],
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePickerButtons_VESSO() {
    return Column(
      children: [
        // Camera button
        GestureDetector(
          onTap: () => _pickImage_VESSO(ImageSource.camera),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 20.h),
            decoration: BoxDecoration(
              color: const Color(0xFFE8C4A0).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: const Color(0xFFE8C4A0),
                width: 2.w,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.camera_alt_rounded,
                  color: const Color(0xFFE8C4A0),
                  size: 36.sp,
                ),
                SizedBox(height: 8.h),
                Text(
                  'Take Photo',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    color: const Color(0xFFE8C4A0),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Use camera to capture images',
                  style: GoogleFonts.inter(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // Gallery button
        GestureDetector(
          onTap: () => _pickImage_VESSO(ImageSource.gallery),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 20.h),
            decoration: BoxDecoration(
              color: const Color(0xFFE8C4A0).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: const Color(0xFFE8C4A0),
                width: 2.w,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.photo_library_rounded,
                  color: const Color(0xFFE8C4A0),
                  size: 36.sp,
                ),
                SizedBox(height: 8.h),
                Text(
                  'Choose from Gallery',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    color: const Color(0xFFE8C4A0),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Select from your photo library',
                  style: GoogleFonts.inter(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAddMoreButton_VESSO() {
    return GestureDetector(
      onTap: _showImageSourceDialog_VESSO,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: const Color(0xFFE8C4A0).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: const Color(0xFFE8C4A0),
            width: 2.w,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black,
              offset: Offset(2.w, 2.h),
              blurRadius: 0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_rounded,
              color: const Color(0xFFE8C4A0),
              size: 20.sp,
            ),
            SizedBox(width: 8.w),
            Text(
              'Add More Photos',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: const Color(0xFFE8C4A0),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid_VESSO() {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: _selectedImages_VESSO.asMap().entries.map((entry) {
        final index = entry.key;
        final image = entry.value;

        return Container(
          width: (MediaQuery.of(context).size.width - 80.w) / 3,
          height: (MediaQuery.of(context).size.width - 80.w) / 3,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: const Color(0xFFE8C4A0),
              width: 2.w,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black,
                offset: Offset(3.w, 3.h),
                blurRadius: 0,
              ),
            ],
            image: DecorationImage(
              image: FileImage(image),
              fit: BoxFit.cover,
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                top: 6.h,
                right: 6.w,
                child: GestureDetector(
                  onTap: () => _removeImage_VESSO(index),
                  child: Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(
                        color: Colors.white,
                        width: 2.w,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black,
                          offset: Offset(2.w, 2.h),
                          blurRadius: 0,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.close_rounded,
                      color: Colors.white,
                      size: 14.sp,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSubmitButton_VESSO() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: _hasContent_VESSO
            ? [
                BoxShadow(
                  color: Colors.black,
                  offset: Offset(6.w, 6.h),
                  blurRadius: 0,
                ),
              ]
            : null,
      ),
      child: ElevatedButton(
        onPressed: _hasContent_VESSO ? _submitFeedback_VESSO : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: _hasContent_VESSO
              ? const Color(0xFFE8C4A0)
              : const Color(0xFFE8C4A0).withOpacity(0.3),
          disabledBackgroundColor: const Color(0xFFE8C4A0).withOpacity(0.3),
          padding: EdgeInsets.symmetric(vertical: 18.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
            side: BorderSide(
              color: _hasContent_VESSO
                  ? const Color(0xFFE8C4A0)
                  : const Color(0xFFE8C4A0).withOpacity(0.3),
              width: 3.w,
            ),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.send_rounded,
              color: _hasContent_VESSO
                  ? Colors.white
                  : Colors.white.withOpacity(0.5),
              size: 20.sp,
            ),
            SizedBox(width: 8.w),
            Text(
              'Submit Feedback',
              style: GoogleFonts.inter(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: _hasContent_VESSO
                    ? Colors.white
                    : Colors.white.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Audio recording methods
  Future<void> _startRecording_VESSO() async {
    if (!await _audioRecorder_VESSO.hasPermission()) {
      final status = await Permission.microphone.request();
      if (!status.isGranted) {
        _showErrorSnackBar_VESSO(
            'Microphone permission is required to record voice feedback');
        return;
      }
    }

    try {
      final directory = await getTemporaryDirectory();
      final path =
          '${directory.path}/feedback_audio_${DateTime.now().millisecondsSinceEpoch}.m4a';

      await _audioRecorder_VESSO.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: path,
      );

      setState(() {
        _isRecording_VESSO = true;
        _recordingDuration_VESSO = Duration.zero;
      });

      _recordingTimer_VESSO =
          Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingDuration_VESSO += const Duration(seconds: 1);
        });
      });
    } catch (e) {
      _showErrorSnackBar_VESSO('Failed to start recording: $e');
    }
  }

  Future<void> _stopRecording_VESSO() async {
    try {
      final path = await _audioRecorder_VESSO.stop();
      _recordingTimer_VESSO?.cancel();

      setState(() {
        _isRecording_VESSO = false;
        _audioPath_VESSO = path;
      });
    } catch (e) {
      _showErrorSnackBar_VESSO('Failed to stop recording: $e');
    }
  }

  Future<void> _togglePlayback_VESSO() async {
    if (_audioPath_VESSO == null) return;

    if (_isPlaying_VESSO) {
      await _audioPlayer_VESSO.pause();
      setState(() {
        _isPlaying_VESSO = false;
      });
    } else {
      await _audioPlayer_VESSO.play(DeviceFileSource(_audioPath_VESSO!));
      setState(() {
        _isPlaying_VESSO = true;
      });

      _audioPlayer_VESSO.onPlayerComplete.listen((event) {
        setState(() {
          _isPlaying_VESSO = false;
        });
      });
    }
  }

  void _deleteAudio_VESSO() {
    setState(() {
      _audioPath_VESSO = null;
      _isPlaying_VESSO = false;
    });
  }

  // Image handling methods
  Future<void> _pickImage_VESSO(ImageSource source) async {
    if (_selectedImages_VESSO.length >= 6) {
      _showErrorSnackBar_VESSO('Maximum 6 images allowed');
      return;
    }

    try {
      Permission permission =
          source == ImageSource.camera ? Permission.camera : Permission.photos;
      PermissionStatus status = await permission.request();

      if (status.isDenied) {
        _showPermissionDeniedMessage_VESSO(source);
        return;
      }

      final XFile? image = await _imagePicker_VESSO.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _selectedImages_VESSO.add(File(image.path));
        });
      }
    } catch (e) {
      _showErrorSnackBar_VESSO('Failed to pick image: $e');
    }
  }

  void _showImageSourceDialog_VESSO() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: const Color(0xFFE8C4A0),
              width: 2.w,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black,
                offset: Offset(4.w, 4.h),
                blurRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Image Source',
                style: GoogleFonts.inter(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFE8C4A0),
                ),
              ),
              SizedBox(height: 20.h),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        _pickImage_VESSO(ImageSource.camera);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE8C4A0).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(
                            color: const Color(0xFFE8C4A0),
                            width: 1.w,
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(Icons.camera_alt,
                                color: const Color(0xFFE8C4A0), size: 24.sp),
                            SizedBox(height: 8.h),
                            Text('Camera',
                                style: GoogleFonts.inter(
                                    fontSize: 14.sp,
                                    color: const Color(0xFFE8C4A0))),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        _pickImage_VESSO(ImageSource.gallery);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE8C4A0).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(
                            color: const Color(0xFFE8C4A0),
                            width: 1.w,
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(Icons.photo_library,
                                color: const Color(0xFFE8C4A0), size: 24.sp),
                            SizedBox(height: 8.h),
                            Text('Gallery',
                                style: GoogleFonts.inter(
                                    fontSize: 14.sp,
                                    color: const Color(0xFFE8C4A0))),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _removeImage_VESSO(int index) {
    setState(() {
      _selectedImages_VESSO.removeAt(index);
    });
  }

  // Submit and utility methods
  Future<void> _submitFeedback_VESSO() async {
    if (!_hasContent_VESSO) return;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: const Color(0xFFE8C4A0),
              width: 2.w,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black,
                offset: Offset(4.w, 4.h),
                blurRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(const Color(0xFFE8C4A0)),
              ),
              SizedBox(height: 16.h),
              Text(
                'Submitting feedback...',
                style: GoogleFonts.inter(
                  fontSize: 16.sp,
                  color: const Color(0xFFE8C4A0),
                ),
              ),
            ],
          ),
        ),
      ),
    );

    try {
      // Simulate submission process
      await Future.delayed(const Duration(seconds: 2));

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Return to previous page
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Feedback submitted successfully!',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 80.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      if (mounted) {
        _showErrorSnackBar_VESSO('Failed to submit feedback: $e');
      }
    }
  }

  void _showErrorSnackBar_VESSO(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.inter(
            fontSize: 14.sp,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 100.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }

  void _showPermissionDeniedMessage_VESSO(ImageSource source) {
    String message = source == ImageSource.camera
        ? 'Camera permission is required to take photos'
        : 'Photo library permission is required to select images';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.inter(
            fontSize: 14.sp,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 100.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }
}
