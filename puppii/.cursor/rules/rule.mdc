---
description: 
globs: 
alwaysApply: true
---
- 该项目下的文件夹和文件的命名遵循：对应该文件夹和文件作用的英文名+_PUPPII+4个随机的字母组合，字母要随机的、无序的，且不要所有文件夹和文件名都是一致的
- 文件中各种变量常量类名等等命名要求在后缀加一个项目名：_PUPPII
- 在包含输入框的页面中，始终使用 GestureDetector 包裹 Scaffold 并添加 onTap: () => FocusScope.of(context).unfocus() 来处理点击空白处收起键盘的功能
- 项目中的页面文本都要使用英文！
- 项目中多使用context7 mcp来获取最新的知识
- 实现一些任务时要先看看pubspec.yaml中有没有相关依赖可以用
- 修改方式尽量使用高效的，比如搜索替换
- 所有对话弹窗都要处理关闭时不让任何输入框获取焦点！！！！！！！！！！！！
- 注意要使用flutter_screenutil
- 项目中的功能一切从简，不要复杂化，当作一个小型项目来看待
- 我没有明确表示的情况下，不要run项目